from lxml import etree
import xml.etree.ElementTree as ET
import base64
import os
import shutil
import pyzipper 
from Cryptodome.Cipher import AES
import hashlib
import datetime
import copy 
from v2.constants import FORM_TABLE_IDS, FORM_EVENTS
from config import settings
import pandas as pd
import os




client_id = settings.NCDR_CLIENT_ID

table_ids = FORM_TABLE_IDS
events = FORM_EVENTS

def get_element_info(xml_file, reference_value):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    for element in root.findall(".//element"):
        if element.get("reference") == reference_value:
            code = element.get("code")
            name = element.find("name").text if element.find("name") is not None else "N/A"
            return {"code": code, "name": name}
    return None

def update_xml_value(xml_file, code, display_name, new_value):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    if "xmlns:xsd" not in root.attrib:
        root.set("xmlns:xsd", "http://www.w3.org/2001/XMLSchema")

    for element in root.findall(".//element"):
        if element.get("code") == code and element.get("displayName") == display_name:
            value_element = element.find("value")
            if value_element is not None:
                xsi_type = value_element.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type")
                if xsi_type == "CD":
                    value_element.attrib.pop("value", None)
                    continue
                if xsi_type == "BL":

                    if new_value is not None and str(new_value).strip():
                        if str(new_value).lower() in ["yes", "true"]:
                            new_value = "true"
                        elif str(new_value).lower() in ["no", "false"]:
                            new_value = "false"
                    else:
                        # For BL type with no value, remove the entire element

                        # Find the parent and remove the element
                        parent = None
                        for p in root.iter():
                            if element in p:
                                parent = p
                                break
                        if parent is not None:
                            parent.remove(element)

                        continue
                elif xsi_type == "DT":
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(str(new_value), "%Y-%m-%d")
                        new_value = date_obj.strftime("%Y-%m-%d")
                    except ValueError:
                        new_value = None
                if new_value is None or str(new_value).strip() == "":
                    value_element.attrib.pop("value", None)
                else:
                    value_element.set("value", new_value)

    # for value_element in root.iter("value"):
    #     xsi_type = value_element.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type")
    #     val = value_element.attrib.get("value")

    #     if val is None or val.strip() == "" or xsi_type == "CD":
    #         value_element.attrib.pop("value", None)

    tree.write(xml_file, encoding='utf-8', xml_declaration=True)
    return xml_file


def get_field_ids_and_values(data: dict, case_id):
    field_data = []
    updated_xml_path = None

    xml_folder = os.path.abspath(rf"v2/data_sources/xml/{case_id}")
    os.makedirs(xml_folder,exist_ok = True)
    original_xml_path = os.path.abspath("v2/data_sources/ncdr/generated_registry_document.xml")
    copied_xml_path = os.path.join(xml_folder, f"LAAO{client_id}-2025Q1.xml")

    shutil.copy(original_xml_path, copied_xml_path)

    def extract_field_data(obj):
        nonlocal updated_xml_path
        if isinstance(obj, dict):
            if "field_id" in obj and "value" in obj:
                field_data.append({"field_id": obj["field_id"], "value": obj["value"], "options": obj.get("options",None), "input_type": obj.get("input_type")})
                if field_data:
                    dict1 = get_element_info(os.path.abspath("v2/data_sources/ncdr/dict.xml"), obj["field_id"])
                    if dict1:
                        updated_xml_path = update_xml_value(copied_xml_path, dict1["code"], dict1["name"], obj["value"])
            for key in obj:
                extract_field_data(obj[key])
        elif isinstance(obj, list):
            for item in obj:
                extract_field_data(item)
    extract_field_data(data)
    if updated_xml_path is None:
        raise ValueError("Failed to update XML file")

    return updated_xml_path

def validate_xml(xml_input):
    """
    Validate XML against XSD schema.
    
    Args:
        xml_input: Can be either a file path (str) or an ElementTree object
        
    Returns:
        List of error messages, empty if validation succeeds
    """
    errors = []
    xsd_file = os.path.abspath("v2/data_sources/ncdr/RTD.xsd")
    
    # Load XSD schema
    with open(xsd_file, 'rb') as f:
        xsd_doc = etree.parse(f)
        xsd_schema = etree.XMLSchema(xsd_doc)
    
    # Handle different input types
    if isinstance(xml_input, str):
        # Input is a file path
        with open(xml_input, 'rb') as f:
            xml_doc = etree.parse(f)
    elif isinstance(xml_input, ET.ElementTree):
        # Input is an ElementTree object
        # Convert ElementTree to lxml.etree for validation
        xml_string = ET.tostring(xml_input.getroot(), encoding='utf-8')
        xml_doc = etree.fromstring(xml_string)
    else:
        raise TypeError("xml_input must be either a file path or an ElementTree object")
    
    # Validate XML
    if xsd_schema.validate(xml_doc):
        return errors
    else:
        for error in xsd_schema.error_log:
            errors.append(f"Line {error.line}: {error.message}")
        return errors

def compute_hash(plain_text: str, hash_algorithm: str = None, salt_bytes: bytes = None) -> str:
    if salt_bytes is None:
        min_salt_size = 4
        max_salt_size = 8
        salt_size = secrets.randbelow(max_salt_size - min_salt_size + 1) + min_salt_size
        salt_bytes = secrets.token_bytes(salt_size)
    plain_text_bytes = plain_text.encode('utf-8')
    plain_text_with_salt_bytes = plain_text_bytes + salt_bytes
    hash_algorithm = (hash_algorithm or '').upper()
    hash_algorithms = {
        'SHA1': hashlib.sha1,
        'SHA256': hashlib.sha256,
        'SHA384': hashlib.sha384,
        'SHA512': hashlib.sha512,
        'MD5': hashlib.md5
    }
    hash_func = hash_algorithms.get(hash_algorithm, hashlib.md5)
    hash_bytes = hash_func(plain_text_with_salt_bytes).digest()
    hash_with_salt_bytes = hash_bytes + salt_bytes
    hash_value = base64.b64encode(hash_with_salt_bytes).decode('utf-8')

    return hash_value

def zip_and_encrypt_file(file_path,case_id, secrets):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File '{file_path}' not found.")
    zip_folder = os.path.abspath(f"v2/data_sources/xml/{case_id}")
    os.makedirs(zip_folder,exist_ok = True)
    zip_file_name = f"LAAO{client_id}-2025Q1.zip"
    zip_file_path = os.path.join(zip_folder, zip_file_name)

    with pyzipper.AESZipFile(zip_file_path, 'w', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zipf:
        zipf.setpassword(secrets.encode())
        zipf.write(file_path, os.path.basename(file_path))


    return zip_file_path

def unzip_and_decrypt_file(zip_file, secrets):
    with pyzipper.AESZipFile(zip_file, 'r', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zipf:
        zipf.setpassword(secrets.encode())
        file_name = zipf.namelist()[0]  # Assuming only one file

        with zipf.open(file_name) as extracted_file:
            extracted_text = extracted_file.read().decode("utf-8")

    return extracted_text


def construct_ncdr_xml(data_dict: dict, case_id: str) -> str:
    xml_folder = os.path.abspath(rf"v2/data_sources/xml/{case_id}")
    os.makedirs(xml_folder,exist_ok = True)
    year = datetime.datetime.now().year
    op_file_path = f"{xml_folder}/LAAO{client_id}-{year}Q1.xml"
    processed_data = extract_fields_with_label(data_dict, table_ids)  # Pass table_ids!

    process_ncdr_data(processed_data, op_file_path, original_data=data_dict)

    return op_file_path


def extract_fields_with_label(data_dict, table_ids=None):
    """
    Traverse through a dictionary and extract fields with a 'label' key.
    Also handles nested conditional fields in if_yes, if_hemorrhage, if_alive, if_deceased.

    Args:
        data_dict (dict): The input dictionary to traverse
        table_ids (list): List of field IDs that should be treated as table fields

    Returns:
        list: A list of dictionaries containing field_id, value, options, and input_type
    """
    if table_ids is None:
        table_ids = []

    result = []



    def traverse(obj, path=None, parent_value=None):
        if path is None:
            path = []

        if isinstance(obj, dict):
            # Check if this is a field with a label
            if 'label' in obj:
                if obj.get('field_id', None) not in table_ids:
                    field_info = {
                        'field_id': obj.get('field_id', None),
                        'label': obj.get('label', None),
                        'value': obj.get('value', None),
                        'options': obj.get('options', None),
                        'input_type': obj.get('input_type', None)
                    }
                    result.append(field_info)

                    # Handle nested conditional fields
                    current_value = obj.get('value', None)

                    # Process if_yes when value is "Yes" or True
                    if current_value in ["Yes", "yes", True, "true"] and 'if_yes' in obj:
                        traverse(obj['if_yes'], path + ['if_yes'], current_value)

                    # Process if_hemorrhage ONLY when current field indicates hemorrhage
                    if current_value in ["Yes", "yes", True, "true"] and 'if_hemorrhage' in obj:
                        traverse(obj['if_hemorrhage'], path + ['if_hemorrhage'], current_value)

                    # Process if_alive when value is "Alive"
                    if current_value in ["Alive", "alive"] and 'if_alive' in obj:
                        traverse(obj['if_alive'], path + ['if_alive'], current_value)

                    # Process if_deceased when value is "Deceased"
                    if current_value in ["Deceased", "deceased"] and 'if_deceased' in obj:
                        traverse(obj['if_deceased'], path + ['if_deceased'], current_value)

            # Continue traversing all key-value pairs
            for key, value in obj.items():
                if key == "field_id" and value in table_ids:

                    result.append(obj)
                    break
                elif key == "field_id" and value == "12153":
                    # Check if this should be added to result
                    if value in table_ids:
                        result.append(obj)

                # Special handling for intra/post-procedure events nested structure
                if key == 'events' and isinstance(value, dict) and 'elements' in value:
                    # This is the intra/post-procedure events structure
                    # Always traverse the events.elements structure to find individual event fields
                    traverse(value, path + [key], parent_value)
                elif key == 'elements' and isinstance(value, dict):
                    # This is the elements container - traverse all categories
                    for category_key, category_value in value.items():
                        if isinstance(category_value, dict):
                            # Traverse each category (cardiovascular, etc.)
                            traverse(category_value, path + [key, category_key], parent_value)
                # Skip conditional blocks that are already processed above
                elif key not in ['if_yes', 'if_hemorrhage', 'if_alive', 'if_deceased', 'events', 'elements']:
                    new_path = path + [key]
                    traverse(value, new_path, parent_value)

        elif isinstance(obj, list):
            for item in obj:
                traverse(item, path, parent_value)

    traverse(data_dict)
    return result

def remove_tag(xml_tree: ET.ElementTree, tag_name: str):

    for parent in xml_tree.getroot():
        for element in parent.findall(f".//{tag_name}"):
            # Skip removal if the element has the preserve attribute set to "true"
            if element.get("preserve") != "true":
                parent.remove(element)

def remove_empty_tags(xml_tree: ET.ElementTree, tag_name: str):
    """
    Removes all instances of the specified tag from the XML tree if they have no child elements,
    regardless of their depth in the tree.

    Parameters:
    xml_tree (ElementTree): The XML tree to modify
    tag_name (str): The name of the tag to remove if empty

    Returns:
    ElementTree: The modified XML tree
    """
    root = xml_tree.getroot()

    _remove_empty_tags_recursive(root, tag_name)

    return xml_tree

def _remove_empty_tags_recursive(element, tag_name):
    """
    Recursively process elements to find and remove empty tags.
    """
    for child in list(element):
        _remove_empty_tags_recursive(child, tag_name)

    for child in list(element):
        # Skip removal if the element has the preserve attribute set to "true"
        if child.tag == tag_name and len(list(child)) == 0 and child.get("preserve") != "true":
            element.remove(child)


def create_or_update_xml_element(tree: ET.ElementTree | ET.Element, attribute_conditions: dict, section_condition: dict, new_value, unit=None, data_type=None):
    """
    Creates a new XML element or updates existing one based on attribute match.
    Used for fields that were removed from template and need to be created dynamically.
    """
    try:
        # First try to update existing element
        sections = get_all_section(tree, section_condition) or [tree]
        element_found = False

        for section in sections:
            for element in section.findall(".//element"):
                if all(element.get(attr) == val for attr, val in attribute_conditions.items()):
                    element_found = True
                    # Update existing element
                    value_tags = element.findall("value")
                    for v in value_tags:
                        if data_type == "BL":
                            v.set("value", str(new_value))
                        elif data_type == "CD":
                            # For CD type, need to find the correct code from selections.csv
                            cd_attrs = get_cd_value_attributes(new_value, attribute_conditions.get('code'))
                            for attr, val in cd_attrs.items():
                                v.set(attr, val)
                        else:
                            v.set("value", str(new_value))
                            if unit:
                                v.set("unit", str(unit))
                    break
            if element_found:
                break

        # If element not found, create new one
        if not element_found:
            # Find the correct PROC section specifically (not PROCINFO)
            target_section = None
            for section in sections:
                if section.get('code') == 'PROC' and section.get('displayName') == 'Procedure':
                    target_section = section
                    break

            if target_section is not None:
                # Create new element
                new_element = create_element('element', attribute_conditions)

                # Create value element based on data type
                if data_type == "BL":
                    value_element = create_element('value', {
                        "xsi:type": "BL",
                        "value": str(new_value)
                    })
                elif data_type == "CD":
                    # For CD type, need to find the correct code from selections.csv
                    value_attrs = get_cd_value_attributes(new_value, attribute_conditions.get('code'))
                    value_element = create_element('value', value_attrs)
                else:
                    value_attrs = {"xsi:type": "ST", "value": str(new_value)}
                    if unit:
                        value_attrs["unit"] = str(unit)
                    value_element = create_element('value', value_attrs)

                new_element.append(value_element)
                target_section.append(new_element)
                print(f"CREATED new XML element: {attribute_conditions.get('displayName')} with value: {new_value} in PROC section")
            else:
                print(f"ERROR: Could not find PROC section to add element: {attribute_conditions.get('displayName')}")

    except Exception as e:
        print(f"Error in create_or_update_xml_element: {e}")


def get_cd_value_attributes(display_name, element_code):
    """
    Get CD value attributes (code, codeSystem, displayName) from selections.csv based on display name
    """
    try:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        selections_path = os.path.join(base_dir, "..", "data_sources", "ncdr", "selections.csv")
        selections = pd.read_csv(selections_path)

        # Find the selection that matches the display name
        matching_rows = selections[selections["Selection Name"] == display_name]

        if not matching_rows.empty:
            row = matching_rows.iloc[0]
            return {
                "xsi:type": "CD",
                "code": str(row["Code"]),
                "codeSystem": str(row["Code System"]),
                "displayName": str(row["Selection Name"])
            }
        else:
            # Fallback - return basic CD attributes
            return {
                "xsi:type": "CD",
                "code": "unknown",
                "codeSystem": "unknown",
                "displayName": str(display_name)
            }
    except Exception as e:
        print(f"Error in get_cd_value_attributes: {e}")
        return {
            "xsi:type": "CD",
            "code": "unknown",
            "codeSystem": "unknown",
            "displayName": str(display_name)
        }


def update_xml_element_by_attributes(tree: ET.ElementTree | ET.Element, attribute_conditions: dict, section_condition: dict, new_value, unit=None):
    """
    Updates XML <element> tags based on attribute match and sets the <value> tag's 'value' attribute.

    Parameters:
        tree (ElementTree): The parsed XML tree.
        attribute_conditions (dict): Attribute conditions to match in <element> tags.
        new_value (str): Value to set in the <value> child element.
        unit (str, optional): Unit to set in the <value> child element for PQ data types.
    """
    sections = get_all_section(tree, section_condition) or [tree]
    for section in sections:
        for element in section.findall(".//element"):
            if all(element.get(attr) == val for attr, val in attribute_conditions.items()):
                value_tags = element.findall("value")
                for v in value_tags:
                    # Handle BL type values specially
                    if v.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL":
                        if new_value is not None and str(new_value).strip():
                            bl_value = str(new_value).lower()
                            v.set("value", "true" if bl_value in ("yes", "true") else "false")
                        else:
                            # For BL type with no value, remove the entire element
                            # Find the parent and remove the element
                            for section in sections:
                                if element in section.findall(".//element"):
                                    section.remove(element)
                                    break
                            continue
                    else: 
                        v.set("value", str(new_value)) 

                    # Add unit attribute if provided and data type is PQ
                    if unit and v.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "PQ":
                        v.set("unit", str(unit))



def filter_element_values_by_code(xml_tree: ET.ElementTree | ET.Element, condition: dict, section_condition: dict, allowed_display_names: list[str]):
    """
    Keeps only <value> tags whose displayName is in the allowed list,
    under an <element> tag that matches the given condition.

    Parameters:
        xml_tree (ElementTree): The parsed XML tree.
        condition (dict): Attribute conditions to match on <element> tag (e.g. {"code": "123", "displayName": "Race"}).
        allowed_display_names (list of str): List of displayName values to keep in <value> tags.
    """
    sections = get_all_section(xml_tree, section_condition) or [xml_tree]
    allowed_set = {name.strip().lower() for name in allowed_display_names}
    


    # Special handling for medication sections
    is_medication_section = False
    section_code = None
    if section_condition and section_condition.get("code") in ["PREPROCMED", "DCMEDS", "ADJMEDS"]:
        is_medication_section = True
        section_code = section_condition.get("code")


    for section in sections:
        elements = section.findall(".//element")
        
        for element in elements:
            if all(element.get(attr) == val for attr, val in condition.items()):
                value_tags = element.findall("value")
                
                # For medication sections, we need special handling
                if is_medication_section:
                    # Check if this is a medication status element
                    is_status_element = (section_code == "PREPROCMED" and element.get("displayName") == "Pre-Procedure Medication Administered") or \
                                       (section_code == "DCMEDS" and element.get("displayName") == "Current Medications at Time of Event") or \
                                       (section_code == "ADJMEDS" and element.get("displayName") == "Current Medications at Time of Event")
                    
                    if is_status_element:
                        # Keep only the value tags that match the allowed codes
                        to_remove = []
                        for value_tag in value_tags:
                            value_code = value_tag.get("code", "").strip().lower()


                            # Check if this value matches any of our allowed codes
                            match_found = False
                            for allowed_code in allowed_display_names:
                                allowed_code_lower = allowed_code.strip().lower()
                                if allowed_code_lower == value_code:
                                    match_found = True
                                    break

                            if not match_found:
                                to_remove.append(value_tag)
                        
                        # Remove the marked tags
                        for tag in to_remove:
                            try:
                                element.remove(tag)
                            except Exception as e:
                                pass
                    else:
                        # For medication name elements, use standard handling
                        for value_tag in value_tags:
                            value_code = value_tag.get("code", "").strip().lower()
                            if value_code not in allowed_set:
                                element.remove(value_tag)
                else:
                    # Standard handling for non-medication sections
                    for value_tag in value_tags:
                        value_code = value_tag.get("code", "").strip().lower()
                        if value_code not in allowed_set:
                            element.remove(value_tag)

                break

def remove_tag_by_condition(xml_tree: ET.ElementTree, tag_name: str, condition: dict, section_condition: dict):
    """
    Removes the first tag with the given tag name and attribute condition from the XML tree.

    Parameters:
        xml_tree (ElementTree): The parsed XML tree.
        tag_name (str): The name of the tag to search for (e.g., 'element', 'value').
        condition (dict): Attributes to match (e.g., {"code": "1234", "displayName": "Race"}).
    """
    removed = False
    sections = get_all_section(xml_tree, section_condition)
    for parent in sections:
        for child in list(parent):  # use list() to avoid mutation during iteration
            if child.tag == tag_name and all(child.get(k) == v for k, v in condition.items()):
                parent.remove(child)
                removed = True
                break
        if removed:
            break

def remove_attribute(xml_tree: ET.ElementTree, tag_name: str, attribute: str):
    xml = xml_tree.getroot()

    for element in xml.iter(tag_name):
        attribute_value = element.attrib.get(attribute)

        # Check if this is a BL type value element
        is_bl_type = element.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL"

        if attribute_value is None:
            # Remove attribute if it doesn't exist
            element.attrib.pop(attribute, None)
        elif attribute_value.strip() == "":
            if is_bl_type:
                # For BL types with empty values, remove the entire element
                # Find the parent element and remove it
                parent_element = None
                for parent in xml.iter():
                    if element in parent:
                        parent_element = parent
                        break
                if parent_element is not None:
                    parent_element.remove(element)
            else:
                # For non-BL types, remove empty attributes
                element.attrib.pop(attribute, None)

def get_all_section(xmltree: ET.ElementTree | ET.Element, condition: dict, remove: bool = False) -> list[ET.Element]:
    sections = []

    if isinstance(xmltree, ET.Element):
        root = xmltree
    else:
        root = xmltree.getroot()

    # Create a parent map for efficient parent lookup
    parent_map = {c: p for p in root.iter() for c in p}

    section_elements = root.findall(".//section")

    for section in section_elements:
        if all(section.get(attr) == val for attr, val in condition.items()):
            sections.append(section)

    if remove and sections:
        for section in sections:
            parent = parent_map.get(section)
            if parent is not None:
                parent.remove(section)

    return sections
def remove_event_tags(xmltree: ET.ElementTree | ET.Element, condition: dict, section_condition: dict):


    sections = get_all_section(xmltree, section_condition) or [xmltree]

    for section in sections:
        elements = section.findall(".//element")
        section_flag = 0
        for ele in elements:
            all_value = ele.findall(".//value")
            for m_val in all_value:
                if section_flag == 0:
                    med = all(m_val.get(attr) == it_val for attr, it_val in condition.items())
                    if med:
                        section.clear()
                        section_flag = 1
                        break
            if section_flag ==1:
                break
        if section_flag==1:
            break


def handle_table_fields(xmltree: ET.ElementTree, elements, selections, field_id: int, data: dict, key_element: str, section_condition: dict):
    table_data = data.get(key_element)
    if not table_data:
        return

    sections = get_all_section(xmltree, section_condition)
    
    # Check if we're dealing with medication sections
    section_code = section_condition.get("code")
    is_medication_section = section_code in ["PREPROCMED", "DCMEDS", "ADJMEDS"]
    is_ippevents_section = section_code == "IPPEVENTS"
    is_hospeveadj_section = section_code == "HOSPEVEADJ"




    # Special handling for IPPEVENTS (Intra or Post-Procedure Events)
    if is_ippevents_section and field_id == 12153:
        # Remove all existing IPPEVENTS sections first
        get_all_section(xmltree, {"code": "IPPEVENTS"}, remove=True)

        # Find the correct parent section to add IPPEVENTS sections to
        # IPPEVENTS should come after INTRAPROCANTICOAG section
        intraproc_section = xmltree.find(".//section[@code='INTRAPROCANTICOAG']")
        if intraproc_section is not None:
            # Find the parent by searching through the tree
            parent_section = None
            for elem in xmltree.iter(): 
                if intraproc_section in elem:
                    parent_section = elem
                    break
        else:
            # Fallback to episode if INTRAPROCANTICOAG not found
            parent_section = xmltree.find(".//episode")

        if parent_section is None:
            return

        # Find the insertion point - should be after INTRAPROCANTICOAG section
        insertion_index = 0
        if intraproc_section is not None:
            # Find the index of INTRAPROCANTICOAG section in its parent
            for i, child in enumerate(parent_section):
                if child == intraproc_section:
                    insertion_index = i + 1  # Insert after INTRAPROCANTICOAG
                    break
        # Get all possible IPPEVENTS from selections.csv for field_id 12153
        all_events = selections[selections['Element Reference'] == field_id]

        # Get element details for the main elements we'll need
        event_element_data = elements[elements['Element Reference'] == field_id]
        occurred_element_data = elements[elements['Element Reference'] == 9002]
        date_element_data = elements[elements['Element Reference'] == 14275]

        if event_element_data.empty:
            return
        if occurred_element_data.empty:
            return

        event_element_info = event_element_data.iloc[0]
        occurred_element_info = occurred_element_data.iloc[0]
        date_element_info = date_element_data.iloc[0] if not date_element_data.empty else None

        # Create a section for each possible event
        for _, event_row in all_events.iterrows():
            event_name = str(event_row['Selection Name'])
            event_code = str(event_row['Code'])
            event_code_system = str(event_row['Code System'])



            # Create new IPPEVENTS section for this event
            ippevents_section = create_element('section', {
                "code": "IPPEVENTS",
                "displayName": "Intra or Post-Procedure Events"
            })

            # Create the main event element
            event_element = create_element('element', {
                "code": str(event_element_info['Code']),
                "codeSystem": str(event_element_info['Code System']),
                "displayName": str(event_element_info['Name'])
            })

            # Add the event value
            event_value_element = create_element('value', {
                "xsi:type": "CD",
                "code": event_code,
                "codeSystem": event_code_system,
                "displayName": event_name
            })
            event_element.append(event_value_element)
            ippevents_section.append(event_element)

            # Determine if this event occurred based on database data
            event_occurred = False
            event_date = None

            # Navigate through the nested database structure
            # Structure: table_data -> elements -> cardiovascular/immunologic/etc -> specific_event
            def search_nested_events(data, target_event_name):
                """Recursively search for event data in nested structure"""
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict):
                            # Check if this is an event with a label
                            if 'label' in value and 'value' in value:
                                db_event_label = value.get("label", "").strip()


                                # Match by label (case-insensitive, handle variations)
                                if (db_event_label.lower() == target_event_name.lower() or
                                    db_event_label.lower().replace(" ", "").replace("-", "") == target_event_name.lower().replace(" ", "").replace("-", "")):

                                    return value  # Return the event data

                            # Recursively search in nested dictionaries
                            result = search_nested_events(value, target_event_name)
                            if result:
                                return result
                return None

            # Search for this event in the database data
            event_data = search_nested_events(table_data, event_name)

            if event_data:
                db_event_value = event_data.get("value", "").strip()

                if db_event_value.lower() in ["yes", "true"]:
                    event_occurred = True

                    # Look for event date in if_yes
                    if 'if_yes' in event_data:
                        if_yes_data = event_data.get('if_yes', {})

                        # Try different possible structures for event date
                        if isinstance(if_yes_data, dict) and "value" in if_yes_data:
                            event_date = if_yes_data.get("value")
                        elif isinstance(if_yes_data, dict) and "event_date" in if_yes_data:
                            event_date_obj = if_yes_data.get("event_date", {})
                            if isinstance(event_date_obj, dict) and "value" in event_date_obj:
                                event_date = event_date_obj.get("value")
                        elif isinstance(if_yes_data, dict) and "14275" in if_yes_data:
                            field_obj = if_yes_data.get("14275", {})
                            if isinstance(field_obj, dict) and "value" in field_obj:
                                event_date = field_obj.get("value")

                        if event_date:
                            pass

            # Create the "Event Occurred" element
            occurred_element = create_element('element', {
                "code": str(occurred_element_info['Code']),
                "codeSystem": str(occurred_element_info['Code System']),
                "displayName": "Event Occurred"  # Use the standard displayName from test.xml
            })

            occurred_value_element = create_element('value', {
                "xsi:type": "BL",
                "value": "true" if event_occurred else "false"
            })
            occurred_element.append(occurred_value_element)
            ippevents_section.append(occurred_element)

            # Add event date if available and event occurred
            if event_occurred and event_date and date_element_info is not None:
                date_element = create_element('element', {
                    "code": str(date_element_info['Code']),
                    "codeSystem": str(date_element_info['Code System']),
                    "displayName": "Event Date(s)"  # Use the standard displayName from test.xml
                })

                date_value_element = create_element('value', {
                    "xsi:type": "DT",
                    "value": event_date
                })
                date_element.append(date_value_element)
                ippevents_section.append(date_element)

            # Insert the IPPEVENTS section at the correct position
            if intraproc_section is not None:
                parent_section.insert(insertion_index, ippevents_section)
                insertion_index += 1  # Increment for next section
            else:
                parent_section.append(ippevents_section)

        # Return early since we've handled the IPPEVENTS section
        return

    # Special handling for HOSPEVEADJ (In-Hospital Adjudication Events)
    if is_hospeveadj_section and field_id == 14312:

        # Find the HOSPEVEADJ section
        hospeveadj_sections = get_all_section(xmltree, {"code": "HOSPEVEADJ"})
        if not hospeveadj_sections:
            return

        hospeveadj_section = hospeveadj_sections[0]

        # Remove all existing Adjudication Event elements
        existing_elements = hospeveadj_section.findall(".//element[@displayName='Adjudication Event']")
        for element in existing_elements:
            hospeveadj_section.remove(element)

        # Remove all existing Adjudication Event Date elements
        existing_date_elements = hospeveadj_section.findall(".//element[@displayName='Adjudication Event Date']")
        for element in existing_date_elements:
            hospeveadj_section.remove(element)

        # Get element details for the main elements we'll need
        event_element_data = elements[elements['Element Reference'] == field_id]
        date_element_data = elements[elements['Element Reference'] == 14313]  # Adjudication Event Date field ID

        if event_element_data.empty:
            return

        event_element_info = event_element_data.iloc[0]
        date_element_info = date_element_data.iloc[0] if not date_element_data.empty else None

        # The table_data for field 14312 contains the selected adjudication event
        # Structure: table_data is the direct field data with 'value' containing the selected event
        selected_event_value = table_data.get('value', '').strip()

        if not selected_event_value:
            return

        # Find the corresponding selection in selections.csv
        event_selection = selections[
            (selections['Element Reference'] == field_id) &
            (selections['Selection Name'].str.strip() == selected_event_value)
        ]

        if event_selection.empty:
            return

        event_selection_info = event_selection.iloc[0]
        event_code = str(event_selection_info['Code'])
        event_code_system = str(event_selection_info['Code System'])
        event_display_name = str(event_selection_info['Selection Name'])



        # Create the Adjudication Event element
        adjudication_element = create_element('element', {
            "code": str(event_element_info['Code']),
            "codeSystem": str(event_element_info['Code System']),
            "displayName": "Adjudication Event"
        })

        # Add the event value
        event_value_element = create_element('value', {
            "xsi:type": "CD",
            "code": event_code,
            "codeSystem": event_code_system,
            "displayName": event_display_name
        })
        adjudication_element.append(event_value_element)
        hospeveadj_section.append(adjudication_element)

        # Add event date if date element info exists
        if date_element_info is not None:
            date_element = create_element('element', {
                "code": str(date_element_info['Code']),
                "codeSystem": str(date_element_info['Code System']),
                "displayName": "Adjudication Event Date"
            })

            # The date should come from field 14313 in the original data
            # We need to look for it in the field_result
            event_date = ""  # Default empty

            date_value_element = create_element('value', {
                "xsi:type": "DT",
                "value": event_date
            })
            date_element.append(date_value_element)
            hospeveadj_section.append(date_element)

        # Return early since we've handled the HOSPEVEADJ section
        return
    
    # If this is a medication section, let's directly find and process all medication status elements first
    if is_medication_section:

        
        # Find all medication status elements in all matching sections
        for section in sections:

            
            # Find the status elements based on section type
            status_display_name = "Pre-Procedure Medication Administered" if section_code == "PREPROCMED" else "Current Medications at Time of Event"
            
            # Find all status elements in this section
            status_elements = section.findall(f".//element[@displayName='{status_display_name}']")

            
            # Process each status element
            for status_element in status_elements:

                
                # Find all value tags in this element
                value_tags = status_element.findall("value")

                
                # Find the medication element that comes before this status element
                med_element = section.find(f".//element[@displayName='Medication']")
                if med_element is not None:
                    med_value = med_element.find("value")
                    if med_value is not None:

                        med_name = med_value.get("displayName")

                        
                        # Find the corresponding value in our data
                        for key, val in table_data.items():
                            if val.get("label") == med_name:
                                selected_value = val.get("value")

                                
                                # Find the code for this selected value
                                value_selection = selections[(selections['Element Reference'] == int(val.get("field_id"))) & 
                                                           (selections['Selection Name'].str.strip() == selected_value)]
                                
                                if not value_selection.empty:
                                    value_code = str(value_selection.iloc[0]['Code'])

                                    
                                    # Keep only the value tag that matches our selected value
                                    to_remove = []
                                    for tag in value_tags:
                                        tag_code = tag.get("code")

                                        
                                        if tag_code != value_code:
                                            to_remove.append(tag)

                                    
                                    # Remove the non-matching tags
                                    for tag in to_remove:
                                        try:
                                            status_element.remove(tag)
                                        except Exception as e:
                                            pass
                                    

                                    
                                    # Break out of the loop once we've found and processed the matching medication
                                    break
    for key, val in table_data.items():
        dos_id = val.get("field_id")
        value = val.get("value")

        event_date = None
        if 'if_yes' in val and value in ["yes", "Yes", True, "true"]:
            event_date = val.get('if_yes',{}).get("value")

        selection_values = selections[(selections['Element Reference'] == field_id) & (selections['Selection Name'].str.strip() == val.get("label"))]

        if not selection_values.empty:
            selection_values = selection_values.iloc[0]
            medicine = {
                "displayName": str(selection_values['Selection Name']),
                "code": str(selection_values['Code']),
                "codeSystem": str(selection_values['Code System'])
            }
            if not value:
                remove_event_tags(xmltree, medicine, section_condition)
                continue

            if str(field_id) in events:
                for section in sections:
                    elements = section.findall(".//element")
                    section_flag = 0

                    for ele in elements:
                        all_value = ele.findall(".//value")
                        for m_val in all_value:
                            if section_flag == 0:
                                med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                if med:
                                    section_flag = 1
                            else:
                                if m_val.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL":
                                    if value is not None and str(value).strip():
                                        m_val.set("value", "true" if value in ["true", "yes", "Yes"] else "false")
                                    else:
                                        # For BL type with no value, remove the entire element

                                        # Find the parent element and remove it
                                        parent_element = None
                                        for section in sections:
                                            for element in section.findall(".//element"):
                                                if m_val in element.findall(".//value"):
                                                    parent_element = element
                                                    break
                                            if parent_element is not None:
                                                break
                                        if parent_element is not None:
                                            parent_section = None
                                            for section in sections:
                                                if parent_element in section.findall(".//element"):
                                                    parent_section = section
                                                    break
                                            if parent_section is not None:
                                                parent_section.remove(parent_element)

                                        continue
                                elif m_val.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "DT":
                                    m_val.set("value", event_date if event_date else "")
                    if section_flag == 1:
                        break
            else:
                # Special handling for medication sections
                if is_medication_section:
                    
                    # For medications, we need to find the value that matches our selection
                    value_selection = None
                    
                    # Handle different medication section types
                    if section_code == "PREPROCMED":
                        # For pre-procedure medications, we're looking for Current/Held/Past/Never
                        value_selection = selections[(selections['Element Reference'] == int(dos_id)) &
                                                    (selections['Selection Name'].str.strip() == value)]
                    elif section_code in ["DCMEDS", "ADJMEDS"]:
                        # For discharge and adjudication medications, we're looking for Yes/No options
                        value_selection = selections[(selections['Element Reference'] == int(dos_id)) &
                                                    (selections['Selection Name'].str.strip() == value)]
                    
                    if not value_selection.empty:
                        value_selection = value_selection.iloc[0]
                        value_code = str(value_selection['Code'])

                        for section in sections:
                            elements = section.findall(".//element")
                            section_flag = 0

                            for ele in elements:
                                # Check if this is the medication element or the status element
                                is_medication_element = ele.get("displayName") == "Medication"
                                is_status_element = (section_code == "PREPROCMED" and ele.get("displayName") == "Pre-Procedure Medication Administered") or \
                                                   (section_code in ["DCMEDS", "ADJMEDS"] and ele.get("displayName") == "Current Medications at Time of Event")

                                all_value = ele.findall(".//value")
                                
                                for m_val in all_value:
                                    if section_flag == 0 and is_medication_element:
                                        med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                        if med:
                                            section_flag = 1
                                    elif section_flag == 1 and is_status_element:
                                        # For medication status elements, keep only the value that matches our selection
                                        # Store values to remove in a separate list to avoid modifying during iteration
                                        to_remove = []
                                        if m_val.get("code") != value_code:
                                            to_remove.append(m_val)

                                        # Remove the values after iteration
                                        for tag in to_remove:
                                            try:
                                                ele.remove(tag)
                                            except Exception as e:
                                                pass
                                
                                # After processing all values in this element
                                if section_flag == 1 and is_status_element:
                                    # Find all values and keep only the one matching our target code
                                    all_values = ele.findall("value")

                                    # Keep track of which values to remove
                                    to_remove = []
                                    for val in all_values:
                                        if val.get("code") != value_code:
                                            to_remove.append(val)

                                    # Remove the non-matching values
                                    for val in to_remove:
                                        try:
                                            ele.remove(val)
                                        except Exception as e:
                                            pass
                            
                            if section_flag == 1:
                                break
                else:
                    # Standard handling for non-medication sections
                    value_selection = selections[(selections['Element Reference'] == int(dos_id)) & (selections['Selection Name'].str.strip() == value)]
                    if not value_selection.empty:
                        value_selection = value_selection.iloc[0]
                        value_set = {
                            # "displayName": str(value_selection['Selection Name']),
                            "code": str(value_selection['Code']),
                            "codeSystem": str(value_selection['Code System'])
                        }

                        for section in sections:
                            elements = section.findall(".//element")
                            section_flag = 0

                            for ele in elements:
                                all_value = ele.findall(".//value")
                                for m_val in all_value:
                                    if section_flag == 0:
                                        med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                        if med:
                                            section_flag = 1
                                    else:
                                        dosage = all(m_val.get(attr) == it_val for attr, it_val in value_set.items())
                                        if not dosage:
                                            ele.remove(m_val)
                            if section_flag == 1:
                                break

def add_element(element: ET.Element, elements: pd.DataFrame, selection: pd.DataFrame, data: dict) -> ET.Element:

    for k, v in data.items():
        field_id = int(v.get("field_id", 0))
        value = v.get("value", 0)
        element_data = elements[elements['Element Reference'] == field_id]


        if element_data.empty:
            return
        element_data = element_data.iloc[0]

        condition = {
                "displayName": str(element_data['Name']),
                "code": str(element_data['Code']),
                "codeSystem": str(element_data['Code System'])
            }
        value_type = str(element_data['Data Type'])
        if value_type == 'BL':
            if value is not None and str(value).strip():
                if value in ["Yes", "yes", "true", True]:
                    value = 'true'
                else:
                    value = 'false'
            else:
                # For BL type with no value, remove the element entirely
                remove_event_tags(element, condition, {})
                continue
        elif not value:
            remove_event_tags(element, condition, {})
            continue

        if value_type == 'CD':
            selection_values = selection[(selection['Element Reference'] == field_id) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

            if not selection_values.empty:
                codes = selection_values['Code'].to_list()
                filter_element_values_by_code(element, condition, {}, codes)

        else:
            update_xml_element_by_attributes(element, condition, {}, value)
    return element
def add_element_with_device_mapping(element: ET.Element, elements: pd.DataFrame, selection: pd.DataFrame, device_elements: pd.DataFrame, data: dict) -> ET.Element:
    """
    Enhanced version of add_element that uses device_elements.csv for device mapping
    """
    for k, v in data.items():
        field_id = int(v.get("field_id", 0))
        value = v.get("value", 0)
        element_data = elements[elements['Element Reference'] == field_id]

        if element_data.empty:
            continue

        element_data = element_data.iloc[0]

        condition = {
            "displayName": str(element_data['Name']),
            "code": str(element_data['Code']),
            "codeSystem": str(element_data['Code System'])
        }

        value_type = str(element_data['Data Type'])

        # Handle boolean values
        if value_type == 'BL':
            if value is not None and str(value).strip():
                if value in ["Yes", "yes", "true", True]:
                    value = 'true'
                else:
                    value = 'false'
            else:
                # For BL type with no value, remove the element entirely
                remove_event_tags(element, condition, {})
                continue
        elif not value:
            remove_event_tags(element, condition, {})
            continue

        # Handle coded values - check if this is a device field
        if value_type == 'CD' and field_id == 14841:  # Device ID field
            # Look up the device in device_elements.csv
            device_data = device_elements[
                device_elements['deviceName'].str.strip() == value.strip()
            ]

            if not device_data.empty:
                device_data = device_data.iloc[0]

                # Create Device element with proper NCDR codes
                device_element = create_element('element', {
                    "code": str(element_data['Code']),  # 63653004
                    "codeSystem": str(element_data['Code System']),  # 2.16.840.1.113883.6.96
                    "displayName": "Device ID"  # Use correct displayName from template
                })

                # Create value element with device info from CSV
                device_value_element = create_element('value', {
                    "xsi:type": "CD",
                    "code": str(device_data['deviceID']),
                    "codeSystem": str(device_data['codeSystem']),  # 2.16.840.1.113883.3.3478.6.1.109
                    "displayName": f"{device_data['deviceName']} ({device_data['modelNumber']})"
                })

                device_element.append(device_value_element)
                element.append(device_element)

                continue
            else:
                # Fall back to original selection-based logic
                pass

        # Original logic for non-device CD fields or fallback
        if value_type == 'CD':
            selection_values = selection[(selection['Element Reference'] == field_id) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

            if not selection_values.empty:
                codes = selection_values['Code'].to_list()
                filter_element_values_by_code(element, condition, {}, codes)
        else:
            # For the add_element_with_device_mapping function, we don't have access to unit info, so pass None
            update_xml_element_by_attributes(element, condition, {}, value, None)

    return element


def create_element(tag_name: str, attributes: dict)-> ET.Element:
    return ET.Element(tag_name, attributes)


def normalize_medication_name(name):
    """
    Normalize medication names to handle different formats between XML and database.
    
    Args:
        name (str): The medication name to normalize.
        
    Returns:
        str: The normalized medication name.
    """
    name = name.lower()
    
    # Handle specific medication name mappings
    name_mappings = {
        "heparin derivative": "heparin_derivative",
        "low molecular weight heparin": "low_molecular_weight_heparin",
        "unfractionated heparin": "unfractionated_heparin",
        "aspirin 81 to 100 mg": "aspirin_81_100_mg",
        "aspirin 101 to 324 mg": "aspirin_101_324_mg",
        "aspirin 325 mg": "aspirin_325_mg",
        "aspirin/dipyridamole": "aspirin_dipyridamole",
        "aspirin": "aspirin",  # Special case for plain aspirin
        "warfarin": "warfarin",
        "fondaparinux": "fondaparinux",
        "apixaban": "apixaban",
        "dabigatran": "dabigatran",
        "edoxaban": "edoxaban",
        "rivaroxaban": "rivaroxaban",
        "cangrelor": "cangrelor",
        "clopidogrel": "clopidogrel",
        "prasugrel": "prasugrel",
        "ticagrelor": "ticagrelor",
        "ticlopidine": "ticlopidine",
        "vorapaxar": "vorapaxar"
    }
    
    # Check for exact matches in the mapping
    if name in name_mappings:
        return name_mappings[name]
    
    # Try to match by removing spaces and special characters
    normalized = name.replace(" ", "_").replace("/", "_").replace("-", "_")
    
    # Additional checks for specific medications
    if name == "aspirin" or name == "aspirin (asa)":
        # For plain aspirin, check all possible aspirin types
        return ["aspirin", "aspirin_81_100_mg", "aspirin_101_324_mg", "aspirin_325_mg"]
    elif "aspirin" in name and ("81" in name or "100" in name):
        return "aspirin_81_100_mg"
    elif "aspirin" in name and ("101" in name or "324" in name):
        return "aspirin_101_324_mg"
    elif "aspirin" in name and "325" in name:
        return "aspirin_325_mg"
    elif "aspirin" in name and "dipyridamole" in name:
        return "aspirin_dipyridamole"
    elif "heparin" in name and "derivative" in name:
        return "heparin_derivative"
    elif "low" in name and "molecular" in name and "heparin" in name:
        return "low_molecular_weight_heparin"
    elif "unfractionated" in name and "heparin" in name:
        return "unfractionated_heparin"
    elif "p2y12" in name:
        return "other_p2y12"
    
    return normalized



def filter_medication_status_elements(xml_tree, field_result=None):
    """
    Directly filter medication status elements in the XML to keep only one value.
    This is a workaround for when we don't have medication data but need to clean up the XML.
    
    Args:
        xml_tree (ElementTree): The XML tree to modify.
        field_result (dict or list): Optional field results from the database.
        
    Returns:
        dict: Event values extracted from field_result.
    """
    # For debugging, write the field_result to a file
    if field_result:
        try:
            import json
            with open("c:/Users/<USER>/Desktop/cm-api/app/logs/field_result_debug.json", "w") as f:
                json.dump(field_result, f, indent=2)
        except Exception as e:
            pass
    
    # Load selection data for medication values
    base_dir = os.path.abspath("v2/data_sources/ncdr")
    selections_file = os.path.join(base_dir, "selections.csv")

    
    try:
        selections = pd.read_csv(selections_file, encoding='utf-8')
    except UnicodeDecodeError:
        selections = pd.read_csv(selections_file, encoding='latin1')
    

    
    # Define the medication sections and their status element names and codes
    medication_sections = {
        "PREPROCMED": {
            "displayName": "Pre-Procedure Medication Administered",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "6985",
            "default": "Past"
        },
        "DCMEDS": {
            "displayName": "Discharge Medication Dose",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "10205",
            "default": "Yes"
        },
        "ADJMEDS": {
            "displayName": "Current Medications at Time of Event",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "14940",
            "default": "Yes"
        },
        "FADJMEDS": {
            "displayName": "Current Medications at Time of Event",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "15006",
            "default": "Yes"
        }
    }
    

    
    # Extract medication values and event values from field_result if provided
    medication_values = {}
    event_values = {}
    if field_result:

        
        # Handle the exact JSON structure provided
        try:
            # Extract intra_or_post_procedure_events
            if 'intra_or_post_procedure_events' in field_result:
                events_data = field_result['intra_or_post_procedure_events']

                if 'events' in events_data and 'elements' in events_data['events']:
                    event_elements = events_data['events']['elements']

                    # Process each category of events
                    for category_name, category_data in event_elements.items():
                        # Process each event in the category
                        for event_name, event_data in category_data.items():
                            # Skip if not a dictionary (some fields might be strings)
                            if not isinstance(event_data, dict):
                                continue

                            event_value = event_data.get('value', '')
                            event_field_id = event_data.get('field_id', '')

                            # Check for event date if event occurred
                            event_date = None
                            if event_value == 'Yes' and 'if_yes' in event_data:
                                event_date = event_data['if_yes'].get('value', '')
                            
                            # Store the event data
                            if 'IPPEVENTS' not in event_values:
                                event_values['IPPEVENTS'] = {}
                            
                            event_values['IPPEVENTS'][event_name.lower()] = {
                                "value": event_value,
                                "field_id": event_field_id,
                                "date": event_date
                            }
            
            # Extract pre_procedure_medications
            # Handle both dictionary format and list format
            pre_proc = None
            if isinstance(field_result, dict) and 'pre_procedure_medications' in field_result:
                pre_proc = field_result['pre_procedure_medications']
            elif isinstance(field_result, list):
                # Look for field with field_id 6985 and key_element 'medication'
                for field in field_result:
                    if field.get('field_id') == '6985' and field.get('key_element') == 'medication':
                        pre_proc = field.get('medication', {})
                        break

            if pre_proc and 'medication' in pre_proc and 'medications' in pre_proc['medication']:
                meds = pre_proc['medication']['medications']

                for med_name, med_data in meds.items():
                    med_value = med_data.get('value', '')
                    med_field_id = med_data.get('field_id', '')

                    if med_value and med_value != 'Never':  # Only include non-Never values
                        if 'PREPROCMED' not in medication_values:
                            medication_values['PREPROCMED'] = {}

                        medication_values['PREPROCMED'][med_name.lower()] = {
                            "value": med_value,
                            "field_id": med_field_id,
                            "code": None
                        }
            
            # Extract discharge_medications
            if 'discharge_medications' in field_result:
                dc_meds = field_result['discharge_medications']

                if 'medication' in dc_meds and 'medications' in dc_meds['medication']:
                    meds = dc_meds['medication']['medications']

                    for med_name, med_data in meds.items():
                        med_value = med_data.get('value', '')
                        med_field_id = med_data.get('field_id', '')

                        # Check for if_yes nested fields
                        if 'if_yes' in med_data and med_value == 'Yes':
                            
                            # Handle dose for aspirin
                            if med_name.lower() == 'aspirin' and 'dose' in med_data['if_yes']:
                                dose_data = med_data['if_yes']['dose']
                                dose_value = dose_data.get('value', '')

                                
                                # Map the dose value to the corresponding medication name
                                if dose_value == "81 - 100 MG":
                                    aspirin_med_name = "aspirin_81_100_mg"
                                elif dose_value == "101 - 324 MG":
                                    aspirin_med_name = "aspirin_101_324_mg"
                                elif dose_value == "325 MG":
                                    aspirin_med_name = "aspirin_325_mg"
                                else:
                                    aspirin_med_name = "aspirin"

                                # Add the aspirin with dose
                                if 'DCMEDS' not in medication_values:
                                    medication_values['DCMEDS'] = {}

                                medication_values['DCMEDS'][aspirin_med_name] = {
                                    "value": med_value,
                                    "field_id": med_field_id,
                                    "code": None
                                }
                            
                        # Add the regular medication
                        if med_value:
                            if 'DCMEDS' not in medication_values:
                                medication_values['DCMEDS'] = {}
                            
                            medication_values['DCMEDS'][med_name.lower()] = {
                                "value": med_value,
                                "field_id": med_field_id,
                                "code": None
                            }
            
            # Extract in_hospital_adjudication
            if 'in_hospital_adjudication' in field_result:
                adj_meds = field_result['in_hospital_adjudication']

                if 'medication' in adj_meds and 'medications' in adj_meds['medication']:
                    meds = adj_meds['medication']['medications']

                    for med_name, med_data in meds.items():
                        med_value = med_data.get('value', '')
                        med_field_id = med_data.get('field_id', '')
                        
                        if med_value:
                            if 'ADJMEDS' not in medication_values:
                                medication_values['ADJMEDS'] = {}
                            
                            medication_values['ADJMEDS'][med_name.lower()] = {
                                "value": med_value,
                                "field_id": med_field_id,
                                "code": None
                            }
        except Exception as e:
            pass
    
    # Process each medication section
    for section_code, section_info in medication_sections.items():
        # Find all sections with this code
        sections = xml_tree.findall(f".//section[@code='{section_code}']")

        # Check if we have any values for this section
        has_values = False
        if section_code in medication_values and medication_values[section_code]:
            # Check if any medication in this section has a value
            for med_name, med_info in medication_values[section_code].items():
                if med_info["value"]:
                    has_values = True
                    break

        # If no values for this section, remove any existing status elements
        if not has_values:
            for section in sections:
                status_elements = section.findall(f".//element[@displayName='{section_info['displayName']}']")
                for status_element in status_elements:
                    section.remove(status_element)
            continue
            
        # Special handling for DCMEDS - remove all status elements and only add them back if we have values
        if section_code == "DCMEDS" or section_code == "ADJMEDS" or section_code == "FADJMEDS":
            # First, remove ALL status elements from ALL sections
            for section in sections:
                # For DCMEDS, we need to handle both displayName values
                if section_code == "DCMEDS":
                    # Try both "Discharge Medication Dose" and "Current Medications at Time of Event"
                    for display_name in ["Discharge Medication Dose", "Current Medications at Time of Event"]:
                        status_elements = section.findall(f".//element[@displayName='{display_name}']")
                        for status_element in status_elements:
                            section.remove(status_element)
                else:
                    status_elements = section.findall(f".//element[@displayName='{section_info['displayName']}']")
                    for status_element in status_elements:
                        section.remove(status_element)

            # Now, process each section and only add status elements for medications with values
            for section in sections:
                
                # Find the medication element
                med_elements = section.findall(".//element[@displayName='Medication']")
                
                for med_element in med_elements:
                    med_value = med_element.find("value")
                    if med_value is not None:
                        med_display_name = med_value.get("displayName", "")
                        med_name = normalize_medication_name(med_display_name)
                        
                        # Check if we have a value for this specific medication
                        med_info = None
                        if section_code in medication_values:
                            # Try the normalized name first
                            if med_name in medication_values[section_code]:
                                med_info = medication_values[section_code][med_name]
                            else:
                                # Try alternative names
                                # Try with just the first word (e.g., "Aspirin" for "Aspirin 325 mg")
                                first_word = med_display_name.split()[0].lower()
                                for db_med_name in medication_values[section_code].keys():
                                    if first_word in db_med_name:
                                        med_info = medication_values[section_code][db_med_name]
                                        break

                        # Skip if no med_info or no value
                        if not med_info:
                            continue

                        # Skip if no value
                        if not med_info.get("value"):
                            continue
                        
                        # Now we have med_info and it has a value
                        med_status = med_info["value"]
                        med_code = med_info["code"]
                        med_field_id = med_info["field_id"]
                        
                        # Create a new status element for this medication
                        status_element = ET.Element("element", {
                            "code": section_info["code"],
                            "codeSystem": section_info["codeSystem"],
                            "displayName": section_info["displayName"]
                        })

                        # If we don't have a code, look it up
                        if not med_code and med_field_id and med_status:
                            try:
                                # First try to look up the code directly
                                value_selection = selections[(selections['Element Reference'] == int(med_field_id)) &
                                                           (selections['Selection Name'].str.strip() == med_status)]

                                if not value_selection.empty:
                                    med_code = str(value_selection.iloc[0]['Code'])
                            except Exception as e:
                                pass

                        # Add the selected value
                        if med_code:
                            # Look up the display name for this code
                            try:
                                code_selection = selections[selections['Code'] == int(med_code)]
                                if not code_selection.empty:
                                    display_name = code_selection.iloc[0]['Selection Name'].strip()
                                else:
                                    display_name = med_status
                            except Exception as e:
                                display_name = med_status

                            # Use the code we found with the correct display name
                            value_tag = ET.Element("value", {
                                "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                                "code": med_code,
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": display_name
                            })
                            status_element.append(value_tag)
                        else:
                            # If we couldn't find a code, use hardcoded values
                            if section_code == "PREPROCMED":
                                if med_status == "Current":
                                    code = "100000987"
                                    display_name = "Current"
                                elif med_status == "Held":
                                    code = "100001010"
                                    display_name = "Held"
                                elif med_status == "Past":
                                    code = "100001070"
                                    display_name = "Past"
                                elif med_status == "Never":
                                    code = "100001071"
                                    display_name = "Never"
                                else:
                                    code = "100001070"  # Default to Past
                                    display_name = "Past"
                            else:  # DCMEDS, ADJMEDS, FADJMEDS
                                if med_status == "Yes":
                                    code = "100001247"
                                    display_name = "Yes"
                                elif med_status == "No - No Reason":
                                    code = "100001048"
                                    display_name = "No - No Reason"
                                elif med_status == "No - Medical Reason":
                                    code = "100001034"
                                    display_name = "No - Medical Reason"
                                elif med_status == "No - Patient Reason":
                                    code = "100001071"
                                    display_name = "No - Patient Reason"
                                elif med_status == "No":
                                    code = "100001048"
                                    display_name = "No"
                                else:
                                    code = "100001247"  # Default to Yes
                                    display_name = "Yes"
                            
                            value_tag = ET.Element("value", {
                                "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                                "code": code,
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": display_name
                            })
                            status_element.append(value_tag)

                        # Add the status element to the section
                        section.append(status_element)

            # Skip the regular processing for these sections
            continue

        # Process each section
        for section in sections:
            # Find all medication elements in this section
            med_elements = section.findall(".//element[@displayName='Medication']")
            
            # First, remove all existing status elements
            status_elements = section.findall(f".//element[@displayName='{section_info['displayName']}']")
            for status_element in status_elements:
                section.remove(status_element)
            
            for med_element in med_elements:
                # Get the medication name
                med_value = med_element.find("value")
                if med_value is not None:
                    med_display_name = med_value.get("displayName", "")
                    med_name = normalize_medication_name(med_display_name)
                   
                    
                    # Check if we have a value for this medication
                    med_info = None
                    if section_code in medication_values:
                        # Handle the case where normalize_medication_name returns a list
                        if isinstance(med_name, list):
                          
                            # Try each name in the list
                            for name in med_name:
                                if name in medication_values[section_code]:
                                    med_info = medication_values[section_code][name]
                                   
                                    break
                        # Try the normalized name first
                        elif med_name in medication_values[section_code]:
                            med_info = medication_values[section_code][med_name]
                           
                        else:
                            # Try alternative names
                           
                            
                            # Try with just the first word (e.g., "Aspirin" for "Aspirin 325 mg")
                            first_word = med_display_name.split()[0].lower()
                            for db_med_name in medication_values[section_code].keys():
                                if first_word in db_med_name:
                                    med_info = medication_values[section_code][db_med_name]
                                   
                                    break
                    
                    # Skip if no med_info or no value
                    if not med_info:
                        continue

                    # Skip if no value
                    if not med_info.get("value"):
                        continue

                    # Now we have med_info and it has a value
                    med_status = med_info["value"]
                    med_code = med_info["code"]
                    med_field_id = med_info["field_id"]

                    # Skip if no value
                    if not med_status:
                        continue

                    # Create a new status element for this medication
                    status_element = ET.Element("element", {
                        "code": section_info["code"],
                        "codeSystem": section_info["codeSystem"],
                        "displayName": section_info["displayName"]
                    })

                    # If we don't have a code, look it up
                    if not med_code and med_field_id and med_status:
                        try:
                            # First try to look up the code directly
                            value_selection = selections[(selections['Element Reference'] == int(med_field_id)) &
                                                       (selections['Selection Name'].str.strip() == med_status)]

                            if not value_selection.empty:
                                med_code = str(value_selection.iloc[0]['Code'])
                        except Exception as e:
                            pass

                    # Add the selected value
                    if med_code:
                        # Look up the display name for this code
                        try:
                            code_selection = selections[selections['Code'] == int(med_code)]
                            if not code_selection.empty:
                                display_name = code_selection.iloc[0]['Selection Name'].strip()
                            else:
                                display_name = med_status
                        except Exception as e:
                            display_name = med_status

                        # Use the code we found with the correct display name
                        value_tag = ET.Element("value", {
                            "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                            "code": med_code,
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": display_name
                        })
                        status_element.append(value_tag)
                    else:
                        # If we couldn't find a code, use hardcoded values
                        if section_code == "PREPROCMED":
                            if med_status == "Current":
                                code = "100000987"
                                display_name = "Current"
                            elif med_status == "Held":
                                code = "100001010"
                                display_name = "Held"
                            elif med_status == "Past":
                                code = "100001070"
                                display_name = "Past"
                            elif med_status == "Never":
                                code = "100001071"
                                display_name = "Never"
                            else:
                                code = "100001070"  # Default to Past
                                display_name = "Past"
                        else:  # DCMEDS, ADJMEDS, FADJMEDS
                            if med_status == "Yes":
                                code = "100001247"
                                display_name = "Yes"
                            elif med_status == "No - No Reason":
                                code = "100001048"
                                display_name = "No - No Reason"
                            elif med_status == "No - Medical Reason":
                                code = "100001034"
                                display_name = "No - Medical Reason"
                            elif med_status == "No - Patient Reason":
                                code = "100001071"
                                display_name = "No - Patient Reason"
                            elif med_status == "No":
                                code = "100001048"
                                display_name = "No"
                            else:
                                code = "100001247"  # Default to Yes
                                display_name = "Yes"

                        value_tag = ET.Element("value", {
                            "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                            "code": code,
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": display_name
                        })
                        status_element.append(value_tag)

                    # Add the status element to the section
                    section.append(status_element)

def fix_multi_value_fields(xmltree, field_data):
    """
    Fix sections that should only show selected values instead of all possible options
    Handles HOSPEVEADJ and History/Risk Factors multi-select fields
    """


    try:
        # 1. Fix HOSPEVEADJ section (Adjudication Events)
        fix_hospeveadj_fields(xmltree, field_data)

        # 2. Fix Left Atrial Appendage Intervention fields
        fix_laa_intervention_fields(xmltree, field_data)

    except Exception as e:
        import traceback
        pass


def fix_hospeveadj_fields(xmltree, field_data):
    """Fix HOSPEVEADJ section fields"""
    try:
        # Get the selected adjudication event and date from field_data
        in_hospital_adj = field_data.get("in_hospital_adjudication", {})
        demographics = in_hospital_adj.get("demographics", {})
        adjudication_event = demographics.get("adjudication_event", {})
        adjudication_event_date = demographics.get("adjudication_event_date", {})

        selected_value = adjudication_event.get("value", "")
        selected_date = adjudication_event_date.get("value", "")

        if not selected_value:
            return

        # Find HOSPEVEADJ section
        hospeveadj_sections = xmltree.findall(".//section[@code='HOSPEVEADJ']")

        if not hospeveadj_sections:
            return

        # Process each HOSPEVEADJ section
        for i, section in enumerate(hospeveadj_sections):
            # Handle Adjudication Event elements (field 14312)
            adj_elements = section.findall(".//element[@displayName='Adjudication Event']")

            for element in adj_elements:
                value_tags = element.findall("value")

                # Find the matching tag
                matching_tag = None
                for value_tag in value_tags:
                    display_name = value_tag.get("displayName", "")
                    if display_name == selected_value:
                        matching_tag = value_tag
                        break

                if matching_tag is not None:
                    # Remove all value tags and add back only the matching one
                    for value_tag in list(value_tags):
                        element.remove(value_tag)
                    element.append(matching_tag)

            # Handle Adjudication Event Date elements (field 14313)
            date_elements = section.findall(".//element[@displayName='Adjudication Event Date']")

            for element in date_elements:
                value_tags = element.findall("value")
                if selected_date:
                    for value_tag in value_tags:
                        value_tag.set("value", selected_date)

    except Exception as e:
        pass


def fix_laa_intervention_fields(xmltree, field_data):
    """Fix Left Atrial Appendage Intervention fields - both main field and intervention type"""
    try:
        # Get the selected left atrial appendage intervention data
        history_interventions = field_data.get("history_interventions", {})
        laa_intervention = history_interventions.get("left_atrial_appendage_occlusion_intervention", {})

        if not laa_intervention:
            return

        laa_value = laa_intervention.get("value", "")

        # Get the selected intervention type (if Yes)
        if_yes = laa_intervention.get("if_yes", {})
        intervention_type = if_yes.get("left_atrial_appendage_intervention_type", {})
        selected_types = intervention_type.get("value", [])

        # For multi-select, get the first selected value
        selected_type = selected_types[0] if isinstance(selected_types, list) and selected_types else ""

        # Find the INTERVENT section where the intervention type element is located
        intervention_sections = xmltree.findall(".//section[@code='INTERVENT']")

        if not intervention_sections:
            return

        for section in intervention_sections:

            # 1. Handle the main "Left Atrial Appendage Occlusion Intervention" element (field 14804)
            # This element might be missing, so we need to create it if needed
            main_elements = section.findall(".//element[@displayName='Left Atrial Appendage Occlusion Intervention']")

            if not main_elements and laa_value:
                # Create the missing main element

                # Find the intervention type element to insert the main element before it
                type_elements = section.findall(".//element[@displayName='Left Atrial Appendage Intervention Type']")
                if type_elements:
                    # Create the main element
                    main_element = ET.Element("element")
                    main_element.set("code", "112000002070")
                    main_element.set("codeSystem", "2.16.840.1.113883.3.3478.6.1")
                    main_element.set("displayName", "Left Atrial Appendage Occlusion Intervention")

                    # Create the value element
                    value_element = ET.Element("value")
                    value_element.set("xsi:type", "BL")
                    # Convert Yes/No to true/false for XSD boolean validation
                    bool_value = "true" if laa_value.lower() == "yes" else "false"
                    value_element.set("value", bool_value)
                    main_element.append(value_element)

                    # Insert before the intervention type element
                    type_element = type_elements[0]
                    parent = section
                    type_index = list(parent).index(type_element)
                    parent.insert(type_index, main_element)


            else:
                # Update existing main element
                for element in main_elements:
                    value_tags = element.findall("value")
                    for value_tag in value_tags:
                        old_value = value_tag.get("value", "")
                        # Convert Yes/No to true/false for XSD boolean validation
                        bool_value = "true" if laa_value.lower() == "yes" else "false"
                        value_tag.set("value", bool_value)


            # 2. Handle the "Left Atrial Appendage Intervention Type" element (field 14806)
            if laa_value == "Yes" and selected_type:
                type_elements = section.findall(".//element[@displayName='Left Atrial Appendage Intervention Type']")

                for element in type_elements:
                    value_tags = element.findall("value")

                    # Find the matching tag
                    matching_tag = None
                    for value_tag in value_tags:
                        display_name = value_tag.get("displayName", "")
                        # Match against the selected type (handle partial matches)
                        if selected_type in display_name or display_name in selected_type:
                            matching_tag = value_tag

                            break

                    if matching_tag is not None:
                        # Remove all value tags and add back only the matching one
                        for value_tag in list(value_tags):
                            element.remove(value_tag)
                        element.append(matching_tag)
            elif laa_value != "Yes":
                # If main intervention is not "Yes", remove the intervention type element
                type_elements = section.findall(".//element[@displayName='Left Atrial Appendage Intervention Type']")
                for element in type_elements:
                    section.remove(element)

    except Exception as e:
        pass


# Keep the old function name for backward compatibility
def fix_hospeveadj_section(xmltree, field_data):
    """Backward compatibility wrapper"""
    fix_multi_value_fields(xmltree, field_data)


def fix_operator_fellow_info(xml_tree: ET.ElementTree, original_data: dict):
    """
    Fix operator and fellow information sections to include actual values from database
    """
    try:
        # Get procedure info from database
        procedure_info = original_data.get("procedureInfo", {}).get("procedure", {})

        # Operator information mapping
        operator_mapping = {
            "operator_first_name": {"field_id": "14860", "xsi_type": "FN", "display_name": "Operator First Name"},
            "operator_last_name": {"field_id": "14861", "xsi_type": "LN", "display_name": "Operator Last Name"},
            "operator_middle_name": {"field_id": "14862", "xsi_type": "MN", "display_name": "Operator Middle Name"},
            "operator_npi": {"field_id": "14863", "xsi_type": "NUM", "display_name": "LAAO Operator NPI"}
        }

        # Fellow information mapping
        fellow_mapping = {
            "fellow_first_name": {"field_id": "15434", "xsi_type": "FN", "display_name": "Fellow First Name"},
            "fellow_last_name": {"field_id": "15433", "xsi_type": "LN", "display_name": "Fellow Last Name"},
            "fellow_middle_name": {"field_id": "15435", "xsi_type": "MN", "display_name": "Fellow Middle Name"},
            "fellow_npi": {"field_id": "15436", "xsi_type": "NUM", "display_name": "Fellow NPI"}
        }

        # Update OPRINFO section
        oprinfo_sections = get_all_section(xml_tree, {"code": "OPRINFO"})
        if oprinfo_sections:
            oprinfo_section = oprinfo_sections[0]


            for db_field, mapping_info in operator_mapping.items():
                field_data = procedure_info.get(db_field, {})
                field_value = field_data.get("value", "")

                if field_value:
                    # Find the corresponding element in XML
                    elements = oprinfo_section.findall(f".//element[@displayName='{mapping_info['display_name']}']")
                    if elements:
                        element = elements[0]
                        # Update the value
                        value_elem = element.find("value")
                        if value_elem is not None:
                            value_elem.set("value", str(field_value))


        # Update FELLOW section
        fellow_sections = get_all_section(xml_tree, {"code": "FELLOW"})
        if fellow_sections:
            fellow_section = fellow_sections[0]


            for db_field, mapping_info in fellow_mapping.items():
                field_data = procedure_info.get(db_field, {})
                field_value = field_data.get("value", "")

                if field_value:
                    # Find the corresponding element in XML
                    elements = fellow_section.findall(f".//element[@displayName='{mapping_info['display_name']}']")
                    if elements:
                        element = elements[0]
                        # Update the value
                        value_elem = element.find("value")
                        if value_elem is not None:
                            value_elem.set("value", str(field_value))
    except Exception as e:
        pass


def fix_boolean_parent_fields(xml_tree: ET.ElementTree, original_data: dict):
    """
    Fix missing boolean parent fields that should appear in XML when their child fields are present
    """
    try:
        # Define the boolean parent fields that need to be added
        boolean_fixes = [
            {
                "field_id": "14824",
                "section_code": "EPICARDIAL",
                "data_path": ["epicardial_access_assessment", "epicardial_approach_considered"],
                "display_name": "Epicardial Approach Considered",
                "code": "112000002078",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14732",
                "section_code": "PROC",
                "data_path": ["procedureInfo", "procedure", "shared_decision_making"],
                "display_name": "Shared Decision Making",
                "code": "112000002041",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14733",
                "section_code": "PROC",
                "data_path": ["procedureInfo", "procedure", "shared_decision_making", "if_yes", "sdm_tool_used"],
                "display_name": "Shared Decision Making Tool Used",
                "code": "415806002",
                "code_system": "2.16.840.1.113883.6.96"
            },
            {
                "field_id": "14834",
                "section_code": "PROC",
                "data_path": ["procedureInfo", "procedure", "procedure_canceled"],
                "display_name": "Procedure Canceled",
                "code": "112000002120",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14846",
                "section_code": "PROC",
                "data_path": ["procedureInfo", "conversion_to_open_heart_surgery", "conversion_to_open_heart_surgery"],
                "display_name": "Conversion to Open Heart Surgery",
                "code": "112000001327",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14831",
                "section_code": "PROC",
                "data_path": ["procedureInfo", "procedure_aborted", "procedure_aborted"],
                "display_name": "Procedure Aborted",
                "code": "112000000515",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14849",
                "section_code": "PROC",
                "data_path": ["device_margin_residual_leak", "device_margin_residual_leak_na"],
                "display_name": "Not Assessed",
                "code": "112000002116",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14802",
                "section_code": "INTERVENT",
                "data_path": ["history_interventions", "cardiac_structural_intervention"],
                "display_name": "Cardiac Structural Intervention",
                "code": "112000002068",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            },
            {
                "field_id": "14804",
                "section_code": "INTERVENT",
                "data_path": ["history_interventions", "left_atrial_appendage_occlusion_intervention"],
                "display_name": "Left Atrial Appendage Occlusion Intervention",
                "code": "112000002070",
                "code_system": "2.16.840.1.113883.3.3478.6.1"
            }
        ]

        for fix in boolean_fixes:
            # Skip SDM fields - they should be processed dynamically from database
            if fix["field_id"] in ["14732", "14733"]:
                continue

            # Navigate to the data value
            data_value = original_data
            for path_part in fix["data_path"]:
                data_value = data_value.get(path_part, {})
                if not isinstance(data_value, dict):
                    break

            # Get the actual value
            field_value = data_value.get("value", "") if isinstance(data_value, dict) else ""

            if field_value:
                # Find the target section
                target_sections = get_all_section(xml_tree, {"code": fix["section_code"]})
                if target_sections:
                    target_section = target_sections[0]

                    # Check if the element already exists (don't override database values)
                    existing_elements = target_section.findall(f".//element[@displayName='{fix['display_name']}']")
                    if not existing_elements:
                        # Create the boolean element
                        bool_element = create_element('element', {
                            "code": fix["code"],
                            "codeSystem": fix["code_system"],
                            "displayName": fix["display_name"]
                        })

                        # Convert value to proper boolean format
                        bl_value = "true" if str(field_value).lower() in ["yes", "true"] else "false"

                        value_element = create_element('value', {
                            "xsi:type": "BL",
                            "value": bl_value
                        })

                        bool_element.append(value_element)

                        # Insert at the beginning of the section
                        target_section.insert(0, bool_element)

    except Exception as e:
        pass


def fix_shared_decision_making_fields(xml_tree: ET.ElementTree, original_data: dict):
    """
    Fix shared decision making nested fields (SDM Tool Name) that need special handling
    """
    try:
        # Navigate to the shared decision making data
        sdm_data = original_data.get("procedureInfo", {}).get("procedure", {}).get("shared_decision_making", {})

        if sdm_data.get("value") == "Yes":
            # Get the nested if_yes data
            if_yes_data = sdm_data.get("if_yes", {})
            sdm_tool_used_data = if_yes_data.get("sdm_tool_used", {})

            if sdm_tool_used_data.get("value") == "Yes":
                # Get the SDM Tool Name data
                sdm_tool_name_data = sdm_tool_used_data.get("if_yes", {}).get("sdm_tool_name", {})
                tool_name_value = sdm_tool_name_data.get("value", "")

                if tool_name_value:
                    # Find the PROC section
                    proc_sections = xml_tree.findall(".//section[@code='PROC']")
                    if proc_sections:
                        proc_section = proc_sections[0]

                        # Check if SDM Tool Name element already exists (don't override database values)
                        existing_tool_name = proc_section.find(".//element[@displayName='Shared Decision Making Tool Name']")
                        if existing_tool_name is None:
                            # Create the SDM Tool Name element
                            tool_name_element = create_element('element', {
                                "code": "405083000",
                                "codeSystem": "2.16.840.1.113883.6.96",
                                "displayName": "Shared Decision Making Tool Name"
                            })

                            # For any text value, map to "Other Shared Decision Making Tool"
                            tool_name_element.append(create_element('value', {
                                "xsi:type": "CD",
                                "code": "100000351",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Other Shared Decision Making Tool"
                            }))

                            # Add to PROC section
                            proc_section.append(tool_name_element)

    except Exception as e:
        pass


def remove_hardcoded_template_elements(xml_tree: ET.ElementTree):
    """
    Remove hardcoded template elements that should be populated from database values
    """
    try:
        # Define elements that have hardcoded values in template and should be database-driven
        hardcoded_elements = [
            {
                "code": "112000000623",  # Procedure Location
                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                "displayName": "Procedure Location"
            },
            {
                "code": "112000002041",  # Shared Decision Making
                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                "displayName": "Shared Decision Making"
            },
            {
                "code": "415806002",  # SDM Tool Used
                "codeSystem": "2.16.840.1.113883.6.96",
                "displayName": "Shared Decision Making Tool Used"
            },
            {
                "code": "405083000",  # SDM Tool Name
                "codeSystem": "2.16.840.1.113883.6.96",
                "displayName": "Shared Decision Making Tool Name"
            }
        ]

        # Remove these elements from the template so they can be populated from database
        for element_info in hardcoded_elements:
            # Find and remove the element from PROC section
            proc_sections = xml_tree.findall(".//section[@code='PROC']")
            for proc_section in proc_sections:
                elements_to_remove = proc_section.findall(f".//element[@code='{element_info['code']}'][@displayName='{element_info['displayName']}']")
                for element in elements_to_remove:
                    proc_section.remove(element)
                    print(f"REMOVED hardcoded template element: {element_info['displayName']} (code: {element_info['code']})")

    except Exception as e:
        pass


def fix_conditional_field_dependencies(xml_tree: ET.ElementTree, original_data: dict):
    """
    Remove conditional fields when their parent condition is not met
    """
    try:
        # Define conditional field dependencies
        conditional_dependencies = [
            {
                "parent_field_path": ["in_hospital_adjudication", "neurologic", "brain_imaging_performed"],
                "parent_required_value": "Yes",
                "child_field_id": "14910",
                "child_display_name": "Deficit Type",
                "section_code": "NEURO"
            },
            {
                "parent_field_path": ["in_hospital_adjudication", "neurologic", "brain_imaging_performed"],
                "parent_required_value": "Yes",
                "child_field_id": "14909",
                "child_display_name": "Brain Imaging Type",
                "section_code": "NEURO"
            },
            {
                "parent_field_path": ["procedureInfo", "procedure_aborted", "procedure_aborted"],
                "parent_required_value": "Yes",
                "child_field_id": "14832",
                "child_display_name": "Procedure Aborted Reason",
                "section_code": "PROC"
            },
            {
                "parent_field_path": ["procedureInfo", "conversion_to_open_heart_surgery", "conversion_to_open_heart_surgery"],
                "parent_required_value": "Yes",
                "child_field_id": "14847",
                "child_display_name": "Conversion to Open Heart Surgery Reason",
                "section_code": "PROC"
            },
            {
                "parent_field_path": ["procedureInfo", "procedure_canceled", "procedure_canceled"],
                "parent_required_value": "Yes",
                "child_field_id": "14833",
                "child_display_name": "Procedure Canceled Reason",
                "section_code": "PROC"
            },
            # Adjudication section conditional dependencies
            {
                "parent_field_path": ["in_hospital_adjudication", "systemic_thromboembolism", "systemic_thromboembolization_imaging_evidence"],
                "parent_required_value": "Yes",
                "child_field_id": "14936",
                "child_display_name": "Imaging Method",
                "section_code": "SYSTHROMB"
            }
        ]

        for dependency in conditional_dependencies:
            # Navigate to the parent field value
            parent_data = original_data
            for path_part in dependency["parent_field_path"]:
                parent_data = parent_data.get(path_part, {})
                if not isinstance(parent_data, dict):
                    break

            # Get the parent field value
            parent_value = parent_data.get("value", "") if isinstance(parent_data, dict) else ""

            # Check if parent condition is met
            if str(parent_value).strip() != dependency["parent_required_value"]:
                # Parent condition not met, remove the child field
                target_sections = get_all_section(xml_tree, {"code": dependency["section_code"]})
                if target_sections:
                    target_section = target_sections[0]

                    # Find and remove the child element
                    child_elements = target_section.findall(f".//element[@displayName='{dependency['child_display_name']}']")
                    for child_element in child_elements:
                        target_section.remove(child_element)


        # Handle adjudication event-based conditional dependencies
        adjudication_event_value = original_data.get("in_hospital_adjudication", {}).get("adjudication_event", {}).get("value", "")

        # Define which sections should be removed based on adjudication event
        adjudication_section_mapping = {
            "NEURO": ["Hemorrhagic Stroke", "Intracranial Hemorrhage", "Ischemic Stroke", "TIA", "Undetermined Stroke"],
            "BLEED": ["Access Site Bleeding", "GI Bleeding", "Hematoma", "Hemothorax", "Other Hemorrhage", "Pericardial Effusion", "Retroperitoneal Bleeding", "Vascular Complications"]
        }
  
        # Remove adjudication status fields from sections that don't match the event type
        for section_code, event_keywords in adjudication_section_mapping.items():
            should_remove_section = True
            for keyword in event_keywords:
                if keyword in adjudication_event_value:
                    should_remove_section = False
                    break

            if should_remove_section:
                target_sections = get_all_section(xml_tree, {"code": section_code})
                if target_sections:
                    target_section = target_sections[0]

                    # Remove Adjudication Status elements from this section
                    status_elements = target_section.findall(".//element[@displayName='Adjudication Status']")
                    for status_element in status_elements:
                        target_section.remove(status_element)


                    # Remove Symptom Onset Date elements from NEURO section if not neurologic event
                    if section_code == "NEURO":
                        symptom_elements = target_section.findall(".//element[@displayName='Symptom Onset Date']")
                        for symptom_element in symptom_elements:
                            target_section.remove(symptom_element)


        # Handle empty sections after conditional field removal
        # XML schema requires sections to have at least one child element
        def remove_empty_sections_recursively(root_element):
            """Recursively remove empty sections from XML tree using ElementTree methods"""
            removed_any = True

            while removed_any:
                removed_any = False

                # Find all parent elements that contain sections
                for parent in root_element.iter():
                    sections_to_remove = []

                    # Check direct child sections
                    for child in parent:
                        if child.tag == "section":
                            # Count child elements (both element and section tags)
                            child_elements = [c for c in child if c.tag in ["element", "section"]]

                            if len(child_elements) == 0:
                                # Section is empty, mark for removal
                                sections_to_remove.append(child)



                    # Remove empty sections from this parent
                    for section in sections_to_remove:
                        parent.remove(section)

                        removed_any = True

        # Start recursive empty section removal from root
        root = xml_tree.getroot()
        remove_empty_sections_recursively(root)

    except Exception as e:
        pass


def process_ncdr_data(field_result: list[dict], op_file_path: str, original_data=None):




        # --- load all lookup tables ---
        base_dir = os.path.abspath("v2/data_sources/ncdr")

        # Try different encodings for CSV files to handle various file formats
        def read_csv_with_encoding(file_path):
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    return pd.read_csv(file_path, encoding=encoding)
                except UnicodeDecodeError:
                    continue
            # If all encodings fail, raise the original error
            return pd.read_csv(file_path)

        selection       = read_csv_with_encoding(os.path.join(base_dir, "selections.csv"))
        elements        = read_csv_with_encoding(os.path.join(base_dir, "elements.csv"))
        access_elements = read_csv_with_encoding(os.path.join(base_dir, "access_system_elements.csv"))
        device_elements = read_csv_with_encoding(os.path.join(base_dir, "device_elements.csv"))

        # parse base XML template
        basexml = ET.parse(os.path.join(base_dir, "generated_registry_document.xml"))

        # Remove hardcoded template elements that should be populated from database
        remove_hardcoded_template_elements(basexml)
        
        # Make a deep copy of the ADMIN section to ensure it's preserved
        admin_section = None
        admin_sections = basexml.findall(".//section[@code='ADMIN']")
        if admin_sections:
            admin_section = copy.deepcopy(admin_sections[0])

        processed_conditions: list[dict] = []

        # Create a mapping of field_ids that were extracted (these are the ones that should exist)
        extracted_field_ids = {str(field.get("field_id")) for field in field_result if field.get("field_id")}

        # Remove elements from XML template that correspond to fields NOT extracted due to conditional logic
        # This handles cases where if_yes, if_alive, etc. fields should be removed when parent condition is not met
        all_elements = elements[elements["Element Reference"].notna()]
        for _, element_row in all_elements.iterrows():
            element_field_id = str(element_row["Element Reference"])

            # If this field was not extracted, remove it from XML template
            if element_field_id not in extracted_field_ids:
                section_cond = {"code": element_row["Section Code"]}
                elem_cond = {
                    "displayName": str(element_row["Name"]),
                    "code": str(element_row["Code"]),
                    "codeSystem": str(element_row["Code System"])
                }
                remove_tag_by_condition(basexml, "element", elem_cond, section_cond)


        for fields in field_result:
            field_id = fields.get("field_id")
            value    = fields.get("value")



            # try cast to int; if not numeric and not a special container or table, skip
            try:
                fid = int(field_id)
            except (ValueError, TypeError):
                if field_id not in table_ids and field_id != "14839_container":
                    continue
                fid = None  # keep going for special flows

            # --- Normal single-element updates ---
            if field_id not in table_ids and field_id != "14839_container":
                if fid is None:
                    continue
                row = elements[elements["Element Reference"] == fid]
                if row.empty:
                    continue
                meta = row.iloc[0]

                section_cond = {"code": meta["Section Code"]}

                # Handle scientific notation in code field
                code_value = str(meta["Code"])
                if "E+" in code_value or "e+" in code_value:
                    # Convert scientific notation to regular number
                    code_value = f"{float(code_value):.0f}"

                elem_cond = {
                    "displayName": str(meta["Name"]),
                    "code":        code_value,
                    "codeSystem":  str(meta["Code System"])
                }
                if elem_cond in processed_conditions:
                    continue

                dtype = str(meta["Data Type"])
                if dtype == "BL":
                    if value is not None and str(value).strip():
                        vlow = str(value).lower()
                        value = "true" if vlow in ("yes", "true") else "false"
                    else:
                        # For BL type with no value, remove the element entirely
                        # This is especially important for HOSPEVEADJ elements
                        remove_tag_by_condition(basexml, "element", elem_cond, section_cond)
                        continue

                if not value and dtype != "BL":
                    remove_tag_by_condition(basexml, "element", elem_cond, section_cond)
                elif dtype == "CD":
                    if isinstance(value, str) and value.strip().lower() in ["alive", "deceased"]:
                        # Directly set the value to "Alive" or "Deceased"
                        value = value.strip().capitalize()
                    elif fid == 12871:  # Procedure Location
                        # Map database value to selection name for procedure location
                        procedure_location_mapping = {
                            "Operating Room": "Operating Room",
                            "Hybrid Operating Room Suite": "Hybrid Operating Room Suite",
                            "Cardiac Catheterization Laboratory": "Cardiac Catheterization Laboratory",
                            "Hybrid Catheterization Laboratory Suite": "Hybrid Catheterization Laboratory Suite",
                            "EP Lab": "EP Lab"
                        }
                        value = procedure_location_mapping.get(value, value)
                    elif fid == 14734:  # SDM Tool Name
                        # For now, map any text value to "Other Shared Decision Making Tool"
                        # since the database has "test" which should map to the "Other" option
                        if value and str(value).strip():
                            value = "Other Shared Decision Making Tool"
                    elif fid in [14312, 14803, 14806]:
                        # Special handling for multi-select CD fields that should only show selected values





                        # Extract the selected values from the field data
                        selected_values = []
                        if isinstance(value, list):
                            selected_values = value
                        elif isinstance(value, str) and value.strip():
                            selected_values = [value]
                        else:
                            selected_values = [str(value)] if value else []

                        if selected_values:
                            # Find matching selections for all selected values
                            selected_codes = []
                            for selected_value in selected_values:
                                if selected_value and selected_value.strip():
                                    sel = selection[
                                        (selection["Element Reference"] == fid) &
                                        (selection["Selection Name"].str.strip() == selected_value.strip())
                                    ]
                                    if not sel.empty:
                                        selected_code = str(sel["Code"].iloc[0])
                                        selected_codes.append(selected_code)


                            if selected_codes:
                                # Remove unwanted value tags, keeping only the selected ones
                                target_sections = get_all_section(basexml, section_cond)
                                for section in target_sections:
                                    xml_elements = section.findall(".//element")
                                    for element in xml_elements:
                                        if all(element.get(attr) == val for attr, val in elem_cond.items()):
                                            value_tags = element.findall("value")


                                            # Remove all value tags except the ones with selected codes
                                            tags_to_remove = []
                                            for value_tag in value_tags:
                                                tag_code = value_tag.get("code", "")

                                                if tag_code not in selected_codes:
                                                    tags_to_remove.append(value_tag)


                                            # Remove the marked tags
                                            for tag in tags_to_remove:
                                                element.remove(tag)


                                            break
                            else:
                                remove_tag_by_condition(basexml, "element", elem_cond, section_cond)
                        else:
                            remove_tag_by_condition(basexml, "element", elem_cond, section_cond)
                    else:
                        sel = selection[
                            (selection["Element Reference"] == fid) &
                            (selection["Selection Name"].str.strip().isin(
                                value if isinstance(value, list) else [value]
                            ))
                        ]
                        if not sel.empty:
                            # Special handling for medication status elements
                            is_medication_status_element = False
                            section_code = section_cond.get("code")
                            
                            # Check if this is a medication section and status element
                            if section_code in ["PREPROCMED", "DCMEDS", "ADJMEDS"]:
                                # For PREPROCMED, the element displayName should be "Pre-Procedure Medication Administered"
                                # For DCMEDS and ADJMEDS, it should be "Current Medications at Time of Event"
                                expected_display_name = "Pre-Procedure Medication Administered" if section_code == "PREPROCMED" else "Current Medications at Time of Event"
                                
                                # Check if the element's displayName matches what we expect for this section
                                if elem_cond.get("displayName") == expected_display_name:
                                    is_medication_status_element = True
                                else:
                                    # If the displayName doesn't match, we need to adjust it
                                    elem_cond["displayName"] = expected_display_name
                                    is_medication_status_element = True
                            
                            if is_medication_status_element:
                                # Pass the code matching the DB value to keep only that value
                                # and remove all other options
                                code_to_keep = sel["Code"].iloc[0]
                                filter_element_values_by_code(basexml, elem_cond, section_cond, [code_to_keep])
                            else:
                                codes = sel["Code"].tolist()
                                filter_element_values_by_code(basexml, elem_cond, section_cond, codes)

                else:
                    # Get unit from database field first, then fall back to elements data
                    unit = fields.get("metric", "")
                    if not unit or str(unit).strip() == "" or str(unit).strip() == "nan":
                        unit = meta.get("Unit Of Measure", "")
                    unit = unit if unit and str(unit).strip() and str(unit).strip() != "nan" else None

                    # For fields that we removed from template, create new elements instead of updating
                    if field_id in ['12871', '14732', '14733', '14734']:
                        create_or_update_xml_element(basexml, elem_cond, section_cond, value, unit, dtype)
                    else:
                        update_xml_element_by_attributes(basexml, elem_cond, section_cond, value, unit)

                processed_conditions.append({**elem_cond, "section_code": str(meta["Section Code"])})

            # --- Special 14839_container flow (Access System) ---
            elif field_id == "14839_container":
                items = fields.get("items", [])
                if not items:
                    continue

                # Find the PROCINFO section to add Access Systems to
                proc_info_section = get_all_section(basexml, {"code": "PROCINFO"})
                if not proc_info_section:
                    continue

                parent_container = proc_info_section[0]

                # Remove any existing Access System sections
                get_all_section(parent_container, {"code": "ACCESSSYS"}, remove=True)

                # Process each access system item
                for idx, access in enumerate(items, 1):
                    # Create Access System section
                    access_sys_element = create_element('section', {
                        "code": "ACCESSSYS",
                        "displayName": "Access Systems"
                    })

                    # Process Access System Counter
                    counter_element = create_element('element', {
                        "code": "112000002110",
                        "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                        "displayName": "Access System Counter"
                    })
                    counter_element.append(
                        create_element('value', {
                            "xsi:type": "CTR",
                            "value": str(access.get("access_system_counter", {}).get("value", idx))
                        })
                    )
                    access_sys_element.append(counter_element)

                    # Process Access System
                    access_system = access.get("access_system_counter", {}).get("template", {}).get("access_system", {})
                    access_system_value = access_system.get("value", "")

                    if access_system_value:
                        # Look up the access system in the access_system_elements.csv
                        access_device_data = access_elements[
                            access_elements['deviceName'].str.strip() == access_system_value.strip()
                        ]

                        if not access_device_data.empty:
                            access_device_data = access_device_data.iloc[0]

                            # Create Access System element
                            access_element = create_element('element', {
                                "code": "112000002110",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Access System"
                            })

                            # Create value element with device info
                            model_number = access_device_data.get('modelNumber', '')
                            display_name = access_device_data['deviceName']
                            if model_number:
                                display_name = f"{display_name} ({model_number})"

                            value_element = create_element('value', {
                                "xsi:type": "CD",
                                "code": str(access_device_data['deviceID']),
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1.108",  # Fixed codeSystem for Access Systems
                                "displayName": display_name
                            })

                            access_element.append(value_element)
                            access_sys_element.append(access_element)
                        else:
                            pass

                    # Process devices
                    devices_container = access_system.get("template", {}).get("devices", {})
                    devices_items = devices_container.get("items", [])

                    # If no items, check if there's a template to process
                    if not devices_items and "template" in devices_container:
                        # This is for handling the structure in the template
                        device_templates = devices_container.get("template", [])
                        for dev_idx, dev_template in enumerate(device_templates, 1):
                            device_counter_obj = dev_template.get("device_counter", {})
                            device_template = device_counter_obj.get("template", {})

                            # Create Device section
                            device_sec = create_element('section', {
                                "code": "DEVICES",
                                "displayName": "Devices"
                            })

                            # Add Device Counter
                            device_counter = create_element('element', {
                                "code": "2.16.840.1.113883.3.3478.4.851",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Device Counter"
                            })
                            device_counter.append(
                                create_element('value', {
                                    "xsi:type": "CTR",
                                    "value": str(dev_idx)
                                })
                            )
                            device_sec.append(device_counter)

                            # Add Device ID element (placeholder for template)
                            device_element = create_element('element', {
                                "code": "63653004",
                                "codeSystem": "2.16.840.1.113883.6.96",
                                "displayName": "Device ID"
                            })
                            # This would be populated with actual data when items are present
                            device_sec.append(device_element)

                            # Add to Access System section
                            access_sys_element.append(device_sec)

                    # Process actual device items if present
                    for dev_idx, dev in enumerate(devices_items, 1):
                        device_counter_obj = dev.get("device_counter", {})
                        device_template = device_counter_obj.get("template", {})

                        # Create Device section
                        device_sec = create_element('section', {
                            "code": "DEVICES",
                            "displayName": "Devices"
                        })

                        # Add Device Counter
                        device_counter = create_element('element', {
                            "code": "2.16.840.1.113883.3.3478.4.851",
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": "Device Counter"
                        })
                        device_counter.append(
                            create_element('value', {
                                "xsi:type": "CTR",
                                "value": str(device_counter_obj.get("value", dev_idx))
                            })
                        )
                        device_sec.append(device_counter)

                        # Add Device element
                        device_info = device_template.get("device", {})
                        device_value = device_info.get("value", "")
                        if device_value:
                            device_row = device_elements[
                                device_elements['deviceName'].str.strip() == device_value.strip()
                            ]
                            if not device_row.empty:
                                dm = device_row.iloc[0]
                                device_element = create_element('element', {
                                    "code": "63653004",
                                    "codeSystem": "2.16.840.1.113883.6.96",
                                    "displayName": "Device ID"
                                })
 
                                model_number = dm.get('modelNumber', '')
                                display_name = dm['deviceName']
                                if model_number:
                                    display_name = f"{display_name} ({model_number})"

                                device_element.append(create_element('value', {
                                    "xsi:type": "CD",
                                    "code": str(dm["deviceID"]),
                                    "codeSystem": "2.16.840.1.113883.3.3478.6.1.109",  # Fixed codeSystem for Devices
                                    "displayName": display_name
                                }))
                                device_sec.append(device_element)
                            else:
                                pass
 
                        # Add UDI if present
                        udi_info = device_template.get("udi", {})
                        udi_value = udi_info.get("value", "")
                        if udi_value:
                            udi_element = create_element('element', {
                                "code": "2.16.840.1.113883.3.3719",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Device UDI Direct Identifier"
                            })
                            udi_element.append(create_element('value', {
                                "xsi:type": "ST",
                                "value": udi_value
                            }))
                            device_sec.append(udi_element)

                        # Add LAA Isolation Approach
                        laa_info = device_template.get("laa_isolation_approach", {})
                        laa_approach = laa_info.get("value", "")
                        if laa_approach:
                            laa_element = create_element('element', {
                                "code": "112000002111",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "LAA Isolation Approach"
                            })

                            # Map approach values to codes
                            approach_code = ""
                            approach_code_system = ""

                            if laa_approach == "Epicardial":
                                approach_code = "112000002078"
                                approach_code_system = "2.16.840.1.113883.3.3478.6.1"
                                approach_display = "Epicardial Access"
                            elif laa_approach == "Percutaneous":
                                approach_code = "103388001"
                                approach_code_system = "2.16.840.1.113883.6.96"
                                approach_display = "Percutaneous Approach"

                            if approach_code:
                                laa_element.append(create_element('value', {
                                    "xsi:type": "CD",
                                    "code": approach_code,
                                    "codeSystem": approach_code_system,
                                    "displayName": approach_display
                                }))
                                device_sec.append(laa_element)

                        # Add Device Successfully Deployed
                        deployed_info = device_template.get("device_successfully_deployed", {})
                        deployed = deployed_info.get("value", "")
                        if deployed is not None and str(deployed).strip():
                            deployed_element = create_element('element', {
                                "code": "1000142349",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Device Successfully Deployed"
                            })

                            # Convert database value to proper BL value
                            bl_value = "true" if str(deployed).lower() in ["yes", "true"] else "false"


                            deployed_element.append(create_element('value', {
                                "xsi:type": "BL",
                                "value": bl_value
                            }))
                            device_sec.append(deployed_element)
                        elif deployed == "":
                            # For empty values, don't create the element at all (following BL fix pattern)
                            pass

                        # Add Reason Device Not Deployed Successfully if applicable
                        # Note: This should be added when device was NOT successfully deployed
                        if str(deployed).lower() in ["no", "false"]:
                            # Look for the reason in the conditional field structure
                            # The structure might be in if_yes (for "No" responses) or directly in the deployed_info
                            reason_info = deployed_info.get("if_yes", {}).get("reason_device_not_deployed_successfully", {})
                            reason = reason_info.get("value", "")



                            if reason:
                                reason_element = create_element('element', {
                                    "code": "112000001662",
                                    "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                    "displayName": "Reason Device Not Deployed Successfully"
                                })

                                # Map reason values to codes
                                reason_code = ""
                                reason_display = ""

                                if reason == "Deployed, not released":
                                    reason_code = "112000002112"
                                    reason_display = "Device Deployed but not Released"
                                elif reason == "Not deployed":
                                    reason_code = "112000002113"
                                    reason_display = "Device not Deployed"
                                elif reason == "Device retrieved":
                                    reason_code = "112000001838"
                                    reason_display = "Device Retrieval"

                                if reason_code:
                                    reason_element.append(create_element('value', {
                                        "xsi:type": "CD",
                                        "code": reason_code,
                                        "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                        "displayName": reason_display
                                    }))
                                    device_sec.append(reason_element)


                        # Add Device section to Access System section
                        access_sys_element.append(device_sec)

                    # Add Access System section to parent container
                    parent_container.append(access_sys_element)

            # --- Table-driven flows remain unchanged ---
            else:
                # Get the key_element and section_code for this field_id
                key_element = {
                    "6985":  "medications",
                    "12153": "elements",
                    "10200": "medications",
                    "14940": "medications",
                    "11990": "elements",
                    "14948": "event_occurred",
                    "14312": "adjudication_event",
                    "15006": "medications"
                }.get(field_id, "")

                section_code = str(
                    elements.loc[
                        elements["Element Reference"] == int(field_id),
                        "Section Code"
                    ].iloc[0]
                )


                handle_table_fields(
                    basexml,
                    elements,
                    selection,
                    int(field_id),
                    fields,
                    key_element,
                    {"code": section_code}
                )

        # --- final cleanup & write ---
        remove_attribute(basexml, "value", "value")
        remove_empty_tags(basexml, "section")
        remove_empty_tags(basexml, "element")
        remove_tag(basexml, "followup")
        
        # Add the ADMIN section back to the XML tree if it was removed during cleanup
        if admin_section is not None:
            submission = basexml.find(".//submission")
            if submission is not None:
                # Check if the ADMIN section still exists
                existing_admin = submission.find(".//section[@code='ADMIN']")
                if existing_admin is None:
                    # If it doesn't exist, add it back
                    submission.insert(0, admin_section)

        # Add Original Patient ID and Original NCDR Vendor elements to demographics section
        def add_original_patient_elements(xml_tree):
            """Add Original Patient ID and Original NCDR Vendor elements to demographics section"""
            try:
                # Find the demographics section
                demographics_sections = xml_tree.findall(".//section[@code='DEMOGRAPHICS']")
                if demographics_sections:
                    demographics_section = demographics_sections[0]

                    # Get the patient ID from the patient element
                    patient_element = xml_tree.find(".//patient")
                    patient_id = patient_element.get("ncdrPatientId", "8145894") if patient_element is not None else "8145894"

                    # Check if Original Patient ID element already exists
                    existing_original_id = demographics_section.find(".//element[@code='112000002061']")
                    if existing_original_id is None:
                        # Create Original Patient ID element
                        original_patient_id_element = create_element('element', {
                            "code": "112000002061",
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": "Original Patient ID"
                        })
                        original_patient_id_value = create_element('value', {
                            "xsi:type": "NUM",
                            "value": patient_id
                        })
                        original_patient_id_element.append(original_patient_id_value)
                        demographics_section.append(original_patient_id_element)


                    # Check if Original NCDR Vendor element already exists
                    existing_vendor = demographics_section.find(".//element[@code='112000002062']")
                    if existing_vendor is None:
                        # Create Original NCDR Vendor element
                        original_vendor_element = create_element('element', {
                            "code": "112000002062",
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": "Original NCDR Vendor"
                        })
                        original_vendor_value = create_element('value', {
                            "xsi:type": "ST",
                            "value": "CorMetrix"
                        })
                        original_vendor_element.append(original_vendor_value)
                        demographics_section.append(original_vendor_element)


            except Exception as e:
                pass

        # Add the original patient elements
        add_original_patient_elements(basexml)

        # Directly filter medication status elements to keep only one value
        filter_medication_status_elements(basexml, original_data if original_data else field_result)
        
        # Validate the XML before writing
        try:
            validate_xml(basexml)
        except Exception as e:
            pass

        # Apply HOSPEVEADJ fix before writing XML
        fix_hospeveadj_section(basexml, original_data)

        # Apply operator and fellow information fix
        fix_operator_fellow_info(basexml, original_data)

        # Apply boolean field fixes for missing parent BL elements (skip SDM fields - they're handled dynamically)
        fix_boolean_parent_fields(basexml, original_data)

        # Apply conditional field removal fixes
        fix_conditional_field_dependencies(basexml, original_data)

        # Apply shared decision making nested field fixes (only if not already processed)
        # fix_shared_decision_making_fields(basexml, original_data)

        # Final cleanup: Remove any remaining empty sections before XML generation
        def final_empty_section_cleanup(xml_tree):
            """Final pass to remove any empty sections that might cause validation errors"""
            root = xml_tree.getroot()
            removed_count = 0

            # Keep removing empty sections until no more are found
            removed_any = True
            while removed_any:
                removed_any = False

                # Find all parent elements that contain sections
                for parent in root.iter():
                    sections_to_remove = []

                    # Check direct child sections
                    for child in parent:
                        if child.tag == "section":
                            # Count child elements (both element and section tags)
                            child_elements = [c for c in child if c.tag in ["element", "section"]]

                            if len(child_elements) == 0:
                                sections_to_remove.append(child)

                    # Remove empty sections from this parent
                    for section in sections_to_remove:

                        parent.remove(section)
                        removed_count += 1
                        removed_any = True
            return removed_count

        # Perform final cleanup
        final_empty_section_cleanup(basexml)

        # Format XML with consistent indentation for readability while maintaining structure
        from xml.dom import minidom
        rough_string = ET.tostring(basexml.getroot(), encoding='utf-8')
        reparsed = minidom.parseString(rough_string)

        # Get pretty formatted XML but remove extra whitespace between tags
        pretty_xml = reparsed.toprettyxml(indent="\t", encoding='utf-8')

        # Convert to string and clean up excessive whitespace
        xml_content = pretty_xml.decode('utf-8')

        # Remove empty lines and excessive whitespace while preserving structure
        lines = xml_content.split('\n')
        cleaned_lines = []
        for line in lines:
            stripped = line.strip()
            if stripped:  # Only keep non-empty lines
                cleaned_lines.append(line)

        # Join lines and encode back to bytes
        final_xml = '\n'.join(cleaned_lines).encode('utf-8')

        # Write the formatted XML to file
        with open(op_file_path, 'wb') as f:
            f.write(final_xml)
        
        # Final validation after writing
        try:
            validate_xml(op_file_path)
        except Exception as e:
            pass